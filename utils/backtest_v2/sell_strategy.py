"""卖出策略组件 - 回测模块V2

基于KOL卖出行为和时间限制确定最优卖出时机，
预先获取整个回测期间的卖出数据，提高查询效率。
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

from dao.kol_wallet_activity_dao import KOLWalletActivityDAO
from utils.backtest_v2.config_manager import BacktestConfigV2
from utils.spiders.smart_money.gmgn_token_candles import GmgnTokenCandlesSpider

logger = logging.getLogger("SellStrategyV2")


class SellStrategy:
    """卖出策略组件"""
    
    def __init__(self, config: BacktestConfigV2):
        """初始化卖出策略组件
        
        Args:
            config: 回测配置对象
        """
        self.config = config
        self.activity_dao = KOLWalletActivityDAO()
        self.price_spider = None  # 延迟初始化
        
        # 从配置中提取参数
        self.sell_strategy_hours = config.sell_strategy_hours
        self.sell_kol_ratio = config.sell_kol_ratio
        self.transaction_lookback_hours = config.transaction_lookback_hours
        self.kol_account_min_txs = config.kol_account_min_txs
        self.kol_account_max_txs = config.kol_account_max_txs
        self.kol_min_winrate = config.kol_min_winrate
        
        # 缓存预查询的卖出数据
        self.sell_data_cache = {}
        
        logger.info(f"卖出策略初始化完成，参数: sell_hours={self.sell_strategy_hours}, "
                   f"sell_ratio={self.sell_kol_ratio}")

    async def _ensure_price_spider(self):
        """确保价格爬虫已初始化"""
        if self.price_spider is None:
            self.price_spider = GmgnTokenCandlesSpider()
            await self.price_spider.setup()

    async def preload_sell_data(self, backtest_start_time: int, backtest_end_time: int):
        """预先查询整个回测期间的KOL卖出数据
        
        Args:
            backtest_start_time: 回测开始时间戳
            backtest_end_time: 回测结束时间戳
        """
        try:
            # 扩展查询时间范围，包含卖出策略时间窗口
            extended_end_time = backtest_end_time + self.sell_strategy_hours * 3600
            
            logger.info(f"开始预加载卖出数据，时间范围: {backtest_start_time} - {extended_end_time}")
            start_time = datetime.now()
            
            # 构建卖出数据聚合管道
            pipeline = await self.build_sell_data_aggregation_pipeline(backtest_start_time, extended_end_time)
            
            # 执行聚合查询
            cursor = self.activity_dao.collection.aggregate(pipeline)
            raw_results = await cursor.to_list(length=None)
            
            # 转换结果格式并缓存
            for result in raw_results:
                token_address = result['_id']
                self.sell_data_cache[token_address] = {
                    'records': result['records'],
                    'kol_wallets': result['kol_wallets'],
                    'kol_wallets_count': result['kol_wallets_count']
                }
            
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            logger.info(f"卖出数据预加载完成，用时: {execution_time:.2f}秒，"
                       f"获得 {len(self.sell_data_cache)} 个token的卖出数据")
            
        except Exception as e:
            logger.error(f"预加载卖出数据失败: {e}")
            raise
    
    async def build_sell_data_aggregation_pipeline(self, start_time: int, end_time: int) -> List[Dict[str, Any]]:
        """构建卖出数据聚合管道
        
        Args:
            start_time: 开始时间戳
            end_time: 结束时间戳
            
        Returns:
            List[Dict[str, Any]]: MongoDB聚合管道
        """
        pipeline = [
            # 1. 时间和事件类型过滤
            {
                '$match': {
                    'timestamp': {'$lte': end_time, '$gte': start_time},
                    'event_type': 'sell'
                }
            },
            # 2. 时间排序
            {'$sort': {'timestamp': 1}},
            # 3. Token分组
            {
                '$group': {
                    '_id': "$token.address",
                    'records': {'$push': "$$ROOT"},
                    'unique_wallets': {'$addToSet': "$wallet"}
                }
            },
            # 4. KOL钱包关联
            {
                '$lookup': {
                    'from': "kol_wallets",
                    'localField': "unique_wallets",
                    'foreignField': "wallet_address",
                    'as': "kol_info_docs"
                }
            },
            # 5. KOL统计数据关联
            {
                '$lookup': {
                    'from': "gmgn_wallet_stats",
                    'localField': "unique_wallets",
                    'foreignField': "wallet_address",
                    'pipeline': [
                        {'$match': {'period': 'all'}},
                        {'$project': {'_id': 0, 'wallet_address': 1, 'winrate': 1}}
                    ],
                    'as': "kol_stats_docs"
                }
            },
            # 6. 构建充实的KOL信息
            {
                '$addFields': {
                    "enriched_kols": {
                        '$map': {
                            'input': '$unique_wallets',
                            'as': 'wallet_addr',
                            'in': {
                                '$let': {
                                    'vars': {
                                        'kol_info': {
                                            '$arrayElemAt': [
                                                {'$filter': {'input': '$kol_info_docs', 'as': 'ki', 'cond': {'$eq': ['$$ki.wallet_address', '$$wallet_addr']}}},
                                                0
                                            ]
                                        },
                                        'kol_stat': {
                                            '$arrayElemAt': [
                                                {'$filter': {'input': '$kol_stats_docs', 'as': 'ks', 'cond': {'$eq': ['$$ks.wallet_address', '$$wallet_addr']}}},
                                                0
                                            ]
                                        }
                                    },
                                    'in': {
                                        'wallet_address': '$$wallet_addr',
                                        'tags': '$$kol_info.tags',
                                        'txs': '$$kol_info.txs',
                                        'winrate': '$$kol_stat.winrate',
                                        '_kol_info_obj': '$$kol_info',
                                        '_kol_stat_obj': '$$kol_stat'
                                    }
                                }
                            }
                        }
                    }
                }
            },
            # 7. 筛选符合所有条件的KOL
            {
                '$addFields': {
                    'kol_wallets': {
                        '$filter': {
                            'input': '$enriched_kols',
                            'as': 'enriched_kol',
                            'cond': {
                                '$and': [
                                    {'$ne': ['$$enriched_kol._kol_info_obj', None]},
                                    {'$ne': ['$$enriched_kol._kol_stat_obj', None]},
                                    {'$ne': ['$$enriched_kol.tags', None]},
                                    {'$isArray': ['$$enriched_kol.tags']},
                                    {'$in': ['kol', '$$enriched_kol.tags']},
                                    {'$gte': ['$$enriched_kol.txs', self.kol_account_min_txs]},
                                    {'$lte': ['$$enriched_kol.txs', self.kol_account_max_txs]},
                                    {'$gt': ['$$enriched_kol.winrate', self.kol_min_winrate]}
                                ]
                            }
                        }
                    }
                }
            },
            # 8. 清理和投影KOL信息
            {
                '$project': {
                    '_id': 1,
                    'records': 1,
                    'kol_wallets': {
                        '$map': {
                            'input': '$kol_wallets',
                            'as': 'selected_kol',
                            'in': {
                                'wallet_address': '$$selected_kol.wallet_address',
                                'tags': '$$selected_kol.tags',
                                'txs': '$$selected_kol.txs',
                                'winrate': '$$selected_kol.winrate'
                            }
                        }
                    }
                }
            },
            # 9. 提取合格KOL钱包地址
            {
                '$addFields': {
                    "qualified_kol_wallet_addresses": {
                        '$map': {
                            'input': "$kol_wallets",
                            'as': "k",
                            'in': "$$k.wallet_address"
                        }
                    }
                }
            },
            # 10. 记录过滤
            {
                '$addFields': {
                    "records": {
                        '$filter': {
                            'input': "$records",
                            'as': "record",
                            'cond': {
                                '$in': ["$$record.wallet", "$qualified_kol_wallet_addresses"]
                            }
                        }
                    }
                }
            },
            # 11. 统计KOL数量
            {
                '$addFields': {
                    'kol_wallets_count': {
                        '$size': "$kol_wallets"
                    }
                }
            },
            # 12. 字段清理
            {
                '$project': {
                    "qualified_kol_wallet_addresses": 0
                }
            }
        ]
        
        logger.debug(f"构建卖出数据聚合管道，时间范围: {start_time} - {end_time}")
        return pipeline
    
    async def determine_sell_signals(self, buy_signals: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """为每个买入信号确定对应的卖出信号
        
        Args:
            buy_signals: 买入信号列表
            
        Returns:
            List[Dict[str, Any]]: 卖出信号列表
        """
        try:
            logger.info(f"开始为 {len(buy_signals)} 个买入信号确定卖出策略")
            
            sell_signals = []
            
            for buy_signal in buy_signals:
                # 尝试KOL比例卖出策略
                kol_sell_signal = await self._kol_ratio_sell_strategy(buy_signal)

                # 计算超时卖出时间
                timeout_sell_signal = await self._timeout_sell_strategy(buy_signal)
                
                # 选择更早的卖出时机
                if kol_sell_signal and timeout_sell_signal:
                    if kol_sell_signal['sell_timestamp'] <= timeout_sell_signal['sell_timestamp']:
                        sell_signals.append(kol_sell_signal)
                    else:
                        sell_signals.append(timeout_sell_signal)
                elif kol_sell_signal:
                    sell_signals.append(kol_sell_signal)
                elif timeout_sell_signal:
                    sell_signals.append(timeout_sell_signal)
                else:
                    logger.warning(f"买入信号 {buy_signal['token_address']}:{buy_signal['signal_timestamp']} "
                                 f"无法确定卖出策略")
            
            logger.info(f"卖出策略确定完成，生成 {len(sell_signals)} 个卖出信号")
            return sell_signals
            
        except Exception as e:
            logger.error(f"确定卖出策略失败: {e}")
            raise
    
    async def _timeout_sell_strategy(self, buy_signal: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """超时卖出策略

        Args:
            buy_signal: 买入信号

        Returns:
            Optional[Dict[str, Any]]: 卖出信号，如果无法确定则返回None
        """
        try:
            buy_timestamp = buy_signal['signal_timestamp']
            sell_timestamp = buy_timestamp + self.sell_strategy_hours * 3600
            token_address = buy_signal['token_address']

            # 获取卖出时间点的价格
            sell_price = await self._get_sell_price(token_address, sell_timestamp)

            return {
                'token_address': token_address,
                'buy_signal_timestamp': buy_timestamp,
                'sell_timestamp': sell_timestamp,
                'sell_reason': 'timeout',
                'avg_price_usd': sell_price if sell_price > 0 else buy_signal.get('avg_price_usd', 0)
            }

        except Exception as e:
            logger.error(f"超时卖出策略计算失败: {e}")
            return None
    
    async def _kol_ratio_sell_strategy(self, buy_signal: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """KOL比例卖出策略
        
        Args:
            buy_signal: 买入信号
            
        Returns:
            Optional[Dict[str, Any]]: 卖出信号，如果无法确定则返回None
        """
        try:
            token_address = buy_signal['token_address']
            buy_timestamp = buy_signal['signal_timestamp']
            buy_kol_wallets = set(buy_signal['kol_wallets'])
            
            # 获取该token的卖出数据
            token_sell_data = self.sell_data_cache.get(token_address)
            if not token_sell_data or not token_sell_data.get('records'):
                return None
            
            sell_records = token_sell_data['records']
            
            # 时间窗口：从买入时间-回溯时间到数据结束时间
            window_start = buy_timestamp - self.transaction_lookback_hours * 3600
            
            # 按时间顺序统计卖出KOL
            sell_kols = set()
            for sell_record in sell_records:
                sell_timestamp = sell_record['timestamp']
                sell_wallet = sell_record['wallet']
                
                # 只考虑买入时间之后的卖出，且是买入信号的KOL
                if (sell_timestamp >= buy_timestamp and 
                    sell_wallet in buy_kol_wallets):
                    sell_kols.add(sell_wallet)
                    
                    # 计算当前卖出比例
                    sell_ratio = len(sell_kols) / len(buy_kol_wallets)
                    
                    # 如果达到比例阈值，返回卖出信号
                    if sell_ratio >= self.sell_kol_ratio:
                        # 获取卖出时间点的价格
                        sell_price = await self._get_sell_price(token_address, sell_timestamp)

                        return {
                            'token_address': token_address,
                            'buy_signal_timestamp': buy_timestamp,
                            'sell_timestamp': sell_timestamp,
                            'sell_reason': 'kol_ratio',
                            'kol_sell_ratio': sell_ratio,
                            'sell_kol_count': len(sell_kols),
                            'total_kol_count': len(buy_kol_wallets),
                            'avg_price_usd': sell_price if sell_price > 0 else buy_signal.get('avg_price_usd', 0)
                        }
            
            # 未达到比例阈值
            return None
            
        except Exception as e:
            logger.error(f"KOL比例卖出策略计算失败: {e}")
            return None

    async def _get_sell_price(self, token_address: str, sell_timestamp: int) -> float:
        """获取卖出时间点的价格

        Args:
            token_address: token地址
            sell_timestamp: 卖出时间戳

        Returns:
            float: 卖出价格，如果无法获取则返回0
        """
        try:
            # 确保价格爬虫已初始化
            await self._ensure_price_spider()

            logger.info(f"使用GMGN接口获取token {token_address} 在时间 {sell_timestamp} 的价格")

            # 使用GMGN接口获取指定时间点的价格
            price_data = await self.price_spider.get_price_point_in_time(
                address=token_address,
                timestamp=sell_timestamp,
                resolution="1m"
            )

            logger.info(f"GMGN接口返回数据: {price_data}")

            if price_data and 'close' in price_data:
                # 使用 'close' 字段作为收盘价
                price = float(price_data['close'])
                logger.info(f"成功获取token {token_address} 在时间 {sell_timestamp} 的价格: {price}")
                return price
            elif price_data and 'c' in price_data:
                # 备用：尝试使用 'c' 字段
                price = float(price_data['c'])
                logger.info(f"成功获取token {token_address} 在时间 {sell_timestamp} 的价格 (c字段): {price}")
                return price
            else:
                logger.warning(f"无法获取token {token_address} 在时间 {sell_timestamp} 的价格数据，返回数据: {price_data}")
                return 0.0

        except Exception as e:
            logger.error(f"获取卖出价格失败: {e}", exc_info=True)
            return 0.0
