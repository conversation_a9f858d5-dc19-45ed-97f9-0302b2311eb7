"""数据查询组件 - 回测模块V2

封装MongoDB聚合查询，提供配置化的数据获取接口。
使用单一聚合管道查询获取完整的回测数据集。
"""

import logging
import asyncio
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime

from dao.kol_wallet_activity_dao import KOLWalletActivityDAO
from dao.token_dao import TokenDAO
from utils.backtest_v2.config_manager import BacktestConfigV2
from utils.spiders.solana.token_info import TokenInfo

logger = logging.getLogger("DataQueryV2")


class DataQuery:
    """数据查询组件"""
    
    def __init__(self, config: BacktestConfigV2):
        """初始化数据查询组件
        
        Args:
            config: 回测配置对象
        """
        self.config = config
        self.activity_dao = KOLWalletActivityDAO()
        self.token_dao = TokenDAO()
        
        # 从配置中提取参数
        self.transaction_min_amount = config.transaction_min_amount
        self.kol_account_min_txs = config.kol_account_min_txs
        self.kol_account_max_txs = config.kol_account_max_txs
        self.kol_account_min_count = config.kol_account_min_count
        self.kol_min_winrate = config.kol_min_winrate
        self.token_mint_lookback_hours = config.token_mint_lookback_hours
    
    async def build_buy_data_aggregation_pipeline(self, start_time: int, end_time: int) -> List[Dict[str, Any]]:
        """构建买入数据聚合管道
        
        Args:
            start_time: 开始时间戳
            end_time: 结束时间戳
            
        Returns:
            List[Dict[str, Any]]: MongoDB聚合管道
        """
        pipeline = [
            # 1. 时间和事件类型过滤
            {
                '$match': {
                    'timestamp': {
                        '$gte': start_time,
                        '$lte': end_time
                    },
                    'event_type': "buy"
                }
            },
            # 2. 数据类型转换
            {
                '$project': {
                    'cost_usd': {
                        '$toDouble': "$cost_usd"
                    },
                    'price_usd': {
                        '$toDouble': "$price_usd"
                    },
                    'token_amount': {
                        '$toDouble': "$token_amount"
                    },
                    'quote_amount': {
                        '$toDouble': "$quote_amount"
                    },
                    'allFields': "$$ROOT"
                }
            },
            # 3. 字段合并
            {
                '$replaceRoot': {
                    'newRoot': {
                        '$mergeObjects': [
                            "$allFields",
                            {
                                'cost_usd': "$cost_usd",
                                'price_usd': "$price_usd",
                                'token_amount': "$token_amount",
                                'quote_amount': "$quote_amount"
                            }
                        ]
                    }
                }
            },
            # 4. 交易金额过滤
            {
                '$match': {
                    'cost_usd': {
                        '$gt': self.transaction_min_amount
                    }
                }
            },
            # 5. 时间排序
            {
                '$sort': {
                    'timestamp': 1
                }
            },
            # 6. Token分组
            {
                '$group': {
                    '_id': "$token.address",
                    'records': {
                        '$push': "$$ROOT"
                    },
                    'unique_wallets': {
                        '$addToSet': "$wallet"
                    }
                }
            },
            # 7. KOL钱包关联
            {
                '$lookup': {
                    'from': "kol_wallets",
                    'localField': "unique_wallets",
                    'foreignField': "wallet_address",
                    'as': "kol_info_docs"
                }
            },
            # 8. KOL统计数据关联
            {
                '$lookup': {
                    'from': "gmgn_wallet_stats",
                    'localField': "unique_wallets",
                    'foreignField': "wallet_address",
                    'pipeline': [
                        {'$match': {'period': 'all'}},
                        {'$project': {'_id': 0, 'wallet_address': 1, 'winrate': 1}}
                    ],
                    'as': "kol_stats_docs"
                }
            },
            # 9. 构建充实的KOL信息
            {
                '$addFields': {
                    "enriched_kols": {
                        '$map': {
                            'input': '$unique_wallets',
                            'as': 'wallet_addr',
                            'in': {
                                '$let': {
                                    'vars': {
                                        'kol_info': {
                                            '$arrayElemAt': [
                                                {'$filter': {'input': '$kol_info_docs', 'as': 'ki', 'cond': {'$eq': ['$$ki.wallet_address', '$$wallet_addr']}}},
                                                0
                                            ]
                                        },
                                        'kol_stat': {
                                            '$arrayElemAt': [
                                                {'$filter': {'input': '$kol_stats_docs', 'as': 'ks', 'cond': {'$eq': ['$$ks.wallet_address', '$$wallet_addr']}}},
                                                0
                                            ]
                                        }
                                    },
                                    'in': {
                                        'wallet_address': '$$wallet_addr',
                                        'tags': '$$kol_info.tags',
                                        'txs': '$$kol_info.txs',
                                        'winrate': '$$kol_stat.winrate',
                                        '_kol_info_obj': '$$kol_info',
                                        '_kol_stat_obj': '$$kol_stat'
                                    }
                                }
                            }
                        }
                    }
                }
            },
            # 10. 筛选符合所有条件的KOL
            {
                '$addFields': {
                    'kol_wallets': {
                        '$filter': {
                            'input': '$enriched_kols',
                            'as': 'enriched_kol',
                            'cond': {
                                '$and': [
                                    {'$ne': ['$$enriched_kol._kol_info_obj', None]},
                                    {'$ne': ['$$enriched_kol._kol_stat_obj', None]},
                                    {'$in': ['kol', '$$enriched_kol.tags']},
                                    {'$gte': ['$$enriched_kol.txs', self.kol_account_min_txs]},
                                    {'$lte': ['$$enriched_kol.txs', self.kol_account_max_txs]},
                                    {'$gt': ['$$enriched_kol.winrate', self.kol_min_winrate]}
                                ]
                            }
                        }
                    }
                }
            },
            # 11. 清理和投影KOL信息
            {
                '$project': {
                    '_id': 1,
                    'records': 1,
                    'kol_wallets': {
                        '$map': {
                            'input': '$kol_wallets',
                            'as': 'selected_kol',
                            'in': {
                                'wallet_address': '$$selected_kol.wallet_address',
                                'tags': '$$selected_kol.tags',
                                'txs': '$$selected_kol.txs',
                                'winrate': '$$selected_kol.winrate'
                            }
                        }
                    }
                }
            },
            # 12. KOL数量统计
            {
                '$addFields': {
                    'kol_wallets_count': {'$size': '$kol_wallets'}
                }
            },
            # 13. Token预筛选
            {
                '$match': {
                    'kol_wallets_count': {'$gte': self.kol_account_min_count}
                }
            },
            # 14. 提取合格KOL钱包地址
            {
                '$addFields': {
                    "qualified_kol_wallet_addresses": {
                        '$map': {
                            'input': "$kol_wallets",
                            'as': "k",
                            'in': "$$k.wallet_address"
                        }
                    }
                }
            },
            # 15. 记录过滤
            {
                '$addFields': {
                    "records": {
                        '$filter': {
                            'input': "$records",
                            'as': "record",
                            'cond': {
                                '$in': ["$$record.wallet", "$qualified_kol_wallet_addresses"]
                            }
                        }
                    }
                }
            },
            # 16. 字段清理
            {
                '$project': {
                    "qualified_kol_wallet_addresses": 0
                }
            }
        ]
        
        logger.debug(f"构建买入数据聚合管道，时间范围: {start_time} - {end_time}")
        return pipeline
    
    async def execute_aggregation_query(self, pipeline: List[Dict[str, Any]]) -> Dict[str, Any]:
        """执行聚合查询并返回结果
        
        Args:
            pipeline: MongoDB聚合管道
            
        Returns:
            Dict[str, Any]: 查询结果，按token地址分组
        """
        try:
            logger.info("开始执行MongoDB聚合查询")
            start_time = datetime.now()
            
            # 执行聚合查询
            cursor = self.activity_dao.collection.aggregate(pipeline)
            raw_results = await cursor.to_list(length=None)
            
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            logger.info(f"聚合查询完成，用时: {execution_time:.2f}秒，获得 {len(raw_results)} 个token")
            
            # 转换结果格式
            token_data_map = {}
            for result in raw_results:
                token_address = result['_id']
                token_data_map[token_address] = {
                    'records': result['records'],
                    'kol_wallets': result['kol_wallets'],
                    'kol_wallets_count': result['kol_wallets_count']
                }
            
            # 验证查询结果
            self.validate_query_result(token_data_map)
            
            return token_data_map
            
        except Exception as e:
            logger.error(f"执行聚合查询失败: {e}")
            raise
    
    def validate_query_result(self, result: Dict[str, Any]) -> bool:
        """验证查询结果的数据完整性
        
        Args:
            result: 查询结果
            
        Returns:
            bool: 验证是否通过
        """
        if not isinstance(result, dict):
            raise ValueError("查询结果必须是字典类型")
        
        for token_address, token_data in result.items():
            if not isinstance(token_data, dict):
                raise ValueError(f"Token {token_address} 的数据必须是字典类型")
            
            required_fields = ['records', 'kol_wallets', 'kol_wallets_count']
            for field in required_fields:
                if field not in token_data:
                    raise ValueError(f"Token {token_address} 缺少必需字段: {field}")
            
            # 验证记录格式
            records = token_data['records']
            if not isinstance(records, list):
                raise ValueError(f"Token {token_address} 的records必须是列表类型")
            
            for record in records:
                if not isinstance(record, dict):
                    raise ValueError(f"Token {token_address} 的记录必须是字典类型")
                
                required_record_fields = ['timestamp', 'wallet', 'cost_usd', 'event_type']
                for field in required_record_fields:
                    if field not in record:
                        raise ValueError(f"Token {token_address} 的记录缺少必需字段: {field}")
        
        logger.debug(f"查询结果验证通过，包含 {len(result)} 个token")
        return True

    async def get_token_info(self, token_addresses: List[str]) -> Dict[str, Any]:
        """获取token基础信息

        Args:
            token_addresses: token地址列表

        Returns:
            Dict[str, Any]: token信息映射
        """
        try:
            logger.info(f"获取 {len(token_addresses)} 个token的基础信息")

            # 批量查询token信息
            tokens = await self.token_dao.find_by_addresses(token_addresses)

            # 转换为映射格式
            token_info_map = {}
            for token in tokens:
                # 处理token可能是字典或对象的情况
                if isinstance(token, dict):
                    address = token.get('address')
                    first_mint_time = token.get('first_mint_time')
                    name = token.get('name', '')
                    symbol = token.get('symbol', '')
                else:
                    # 假设是对象
                    address = getattr(token, 'address', None)
                    first_mint_time = getattr(token, 'first_mint_time', None)
                    name = getattr(token, 'name', '')
                    symbol = getattr(token, 'symbol', '')

                if address:
                    token_info_map[address] = {
                        'address': address,
                        'first_mint_time': first_mint_time,
                        'name': name,
                        'symbol': symbol
                    }

            # 检查缺失的token并尝试获取
            missing_tokens = set(token_addresses) - set(token_info_map.keys())
            if missing_tokens:
                logger.info(f"尝试获取 {len(missing_tokens)} 个缺失token的信息")
                await self._fetch_missing_token_info(missing_tokens, token_info_map)

            logger.info(f"成功获取 {len(token_info_map)} 个token的基础信息")
            return token_info_map

        except Exception as e:
            logger.error(f"获取token信息失败: {e}")
            raise

    async def _fetch_missing_token_info(self, missing_tokens: set, token_info_map: Dict[str, Any]):
        """获取缺失的token信息

        Args:
            missing_tokens: 缺失的token地址集合
            token_info_map: token信息映射，会被直接修改
        """
        try:
            # 限制并发数量，避免过多请求
            semaphore = asyncio.Semaphore(5)

            async def fetch_single_token(address: str):
                async with semaphore:
                    try:
                        logger.debug(f"获取token信息: {address}")
                        token_info_fetcher = TokenInfo(address)
                        token_info = await token_info_fetcher.get_token_info()

                        if token_info and token_info.get('first_mint_time') is not None:
                            first_mint_time = token_info.get('first_mint_time')
                            token_info_map[address] = {
                                'address': address,
                                'first_mint_time': first_mint_time,
                                'name': token_info.get('name', ''),
                                'symbol': token_info.get('symbol', '')
                            }
                            logger.info(f"成功获取token {address} 的信息: first_mint_time={first_mint_time} (类型: {type(first_mint_time)})")
                        else:
                            first_mint_time = token_info.get('first_mint_time') if token_info else None
                            logger.warning(f"无法获取token {address} 的有效信息: token_info存在={bool(token_info)}, first_mint_time={first_mint_time}")
                    except Exception as e:
                        logger.warning(f"获取token {address} 信息失败: {e}")

            # 并发获取缺失的token信息
            tasks = [fetch_single_token(address) for address in missing_tokens]
            await asyncio.gather(*tasks, return_exceptions=True)

            # 统计获取结果
            fetched_count = len([addr for addr in missing_tokens if addr in token_info_map])
            logger.info(f"成功获取 {fetched_count}/{len(missing_tokens)} 个缺失token的信息")

        except Exception as e:
            logger.error(f"获取缺失token信息失败: {e}")
            # 不抛出异常，允许回测继续进行

    async def filter_new_token_records(self, token_data_map: Dict[str, Any],
                                     token_info_map: Dict[str, Any]) -> Dict[str, Any]:
        """对每个token的交易记录进行新代币时间过滤

        Args:
            token_data_map: token数据映射
            token_info_map: token信息映射

        Returns:
            Dict[str, Any]: 过滤后的token数据
        """
        try:
            logger.info("开始新代币记录过滤和KOL数量预筛选")

            filtered_data = {}
            lookback_seconds = self.token_mint_lookback_hours * 3600

            for token_address, token_data in token_data_map.items():
                # 获取token的mint时间
                token_info = token_info_map.get(token_address)
                if not token_info or token_info.get('first_mint_time') is None:
                    first_mint_time = token_info.get('first_mint_time') if token_info else None
                    logger.warning(f"Token {token_address} 缺少mint时间信息，跳过: token_info存在={bool(token_info)}, first_mint_time={first_mint_time}")
                    continue

                mint_timestamp = token_info['first_mint_time']
                records = token_data['records']
                
                logger.info(f"Token {token_address} 时间过滤: mint_timestamp={mint_timestamp} (类型: {type(mint_timestamp)}), 记录数: {len(records)}, lookback_seconds={lookback_seconds}")

                # 确保mint_timestamp是整数时间戳
                if isinstance(mint_timestamp, datetime):
                    mint_timestamp = int(mint_timestamp.timestamp())
                    logger.info(f"Token {token_address} 转换时间戳: {mint_timestamp}")
                elif mint_timestamp is None:
                    logger.warning(f"Token {token_address} 缺少mint时间，跳过")
                    continue

                # 过滤买入记录：只保留在新代币时期内的记录
                filtered_records = []
                for record in records:
                    record_timestamp = record['timestamp']
                    time_diff = record_timestamp - mint_timestamp
                    if time_diff <= lookback_seconds:
                        filtered_records.append(record)
                        logger.debug(f"Token {token_address} 记录保留: record_timestamp={record_timestamp}, time_diff={time_diff}秒")
                    else:
                        logger.debug(f"Token {token_address} 记录过滤: record_timestamp={record_timestamp}, time_diff={time_diff}秒 > {lookback_seconds}秒")
                        
                logger.info(f"Token {token_address} 时间过滤结果: 原始 {len(records)} 条记录，过滤后 {len(filtered_records)} 条记录")

                # 统计过滤后的唯一KOL数量
                unique_kol_wallets = set()
                for record in filtered_records:
                    unique_kol_wallets.add(record['wallet'])

                # KOL数量预筛选：如果唯一KOL数量不足，直接移除该token
                if len(unique_kol_wallets) < self.kol_account_min_count:
                    logger.debug(f"Token {token_address} 过滤后只有 {len(unique_kol_wallets)} 个唯一KOL，少于阈值 {self.kol_account_min_count}，移除")
                    continue

                # 保留该token的过滤后数据
                filtered_data[token_address] = {
                    'records': filtered_records,
                    'kol_wallets': token_data['kol_wallets'],
                    'kol_wallets_count': len(unique_kol_wallets)  # 更新为实际的唯一KOL数量
                }

                logger.debug(f"Token {token_address}: 原始记录 {len(records)} 条，过滤后 {len(filtered_records)} 条，唯一KOL {len(unique_kol_wallets)} 个")

            logger.info(f"新代币记录过滤完成，从 {len(token_data_map)} 个token过滤到 {len(filtered_data)} 个token")
            return filtered_data

        except Exception as e:
            logger.error(f"新代币记录过滤失败: {e}")
            raise
