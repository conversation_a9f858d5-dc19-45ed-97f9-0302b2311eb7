"""回测执行引擎 - 回测模块V2

统筹整个回测流程，集成各个组件，处理组件间的数据流转，
提供进度监控和错误处理。
"""

import asyncio
import logging
import time
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

from utils.backtest_v2.config_manager import BacktestConfigV2
from utils.backtest_v2.data_query import DataQuery
from utils.backtest_v2.signal_analyzer import SignalAnalyzer
from utils.backtest_v2.sell_strategy import SellStrategy
from utils.backtest_v2.result_analyzer import ResultAnalyzer

logger = logging.getLogger("BacktestEngineV2")


class BacktestEngineV2:
    """回测执行引擎V2"""
    
    def __init__(self, config: BacktestConfigV2):
        """初始化回测引擎
        
        Args:
            config: 回测配置对象
        """
        self.config = config
        self.result_dir = None  # 结果输出目录，由外部设置
        
        # 初始化数据查询和卖出策略组件
        self.data_query = DataQuery(config)
        self.sell_strategy = SellStrategy(config)
        
        # 信号分析器和结果分析器需要在预加载卖出数据后初始化
        self.signal_analyzer = None
        self.result_analyzer = None
        
        logger.info("回测引擎V2初始化完成")
    
    async def run_backtest(self) -> Dict[str, Any]:
        """执行完整的回测流程
        
        Returns:
            Dict[str, Any]: 回测结果
        """
        try:
            logger.info("开始执行回测模块V2流程")
            start_time = time.time()

            # 步骤1: 执行买入数据聚合查询（先过滤出符合条件的token）
            logger.info("步骤1: 执行买入数据聚合查询")
            buy_pipeline = await self.data_query.build_buy_data_aggregation_pipeline(
                self.config.backtest_start_time,
                self.config.backtest_end_time
            )
            raw_token_data = await self.data_query.execute_aggregation_query(buy_pipeline)
            logger.info(f"买入数据查询完成，获得 {len(raw_token_data)} 个token")

            # 步骤2: 获取token信息（mint时间等）
            logger.info("步骤2: 获取token基础信息")
            token_addresses = list(raw_token_data.keys())
            token_info_map = await self.data_query.get_token_info(token_addresses)

            # 步骤3: 新代币记录过滤 + KOL数量预筛选
            logger.info("步骤3: 新代币记录过滤和KOL数量预筛选")
            filtered_token_data = await self.data_query.filter_new_token_records(raw_token_data, token_info_map)
            logger.info(f"过滤后剩余 {len(filtered_token_data)} 个token进入信号分析")

            # 步骤4: 初始化信号分析器（暂时不需要卖出数据缓存）
            logger.info("步骤4: 初始化信号分析器")
            self.signal_analyzer = SignalAnalyzer(self.config, {})  # 暂时传入空的卖出数据缓存
            self.result_analyzer = ResultAnalyzer(self.config)

            # 步骤5: 信号检测（包含买入即卖出过滤）- 并发处理
            logger.info("步骤5: 并发执行信号检测")
            buy_signals = await self._analyze_signals_concurrent(filtered_token_data)
            logger.info(f"信号检测完成，生成 {len(buy_signals)} 个买入信号")

            # 步骤6: 按需加载卖出数据（只为有买入信号的token加载）
            logger.info("步骤6: 按需加载卖出数据")
            if buy_signals:
                # 提取有买入信号的token地址
                signal_token_addresses = list(set(signal['token_address'] for signal in buy_signals))
                logger.info(f"需要加载 {len(signal_token_addresses)} 个token的卖出数据")

                # 扩展时间范围以包含卖出策略时间窗口
                extended_end_time = self.config.backtest_end_time + self.config.sell_strategy_hours * 3600
                await self.sell_strategy.preload_sell_data_for_tokens(
                    signal_token_addresses,
                    self.config.backtest_start_time,
                    extended_end_time
                )
            else:
                logger.info("没有买入信号，跳过卖出数据加载")

            # 步骤7: 卖出策略（基于按需加载的卖出数据）
            logger.info("步骤7: 执行卖出策略")
            sell_signals = await self.sell_strategy.determine_sell_signals(buy_signals)

            # 步骤8: 收益计算
            logger.info("步骤8: 计算交易收益")
            trades = self._calculate_trades(buy_signals, sell_signals)

            # 步骤9: 结果分析
            logger.info("步骤9: 生成结果分析")
            results = self.result_analyzer.analyze(trades)
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            # 添加执行时间信息
            results['execution_time'] = execution_time
            results['execution_time_formatted'] = str(timedelta(seconds=execution_time))
            results['backtest_version'] = 'v2'
            
            logger.info(f"回测流程执行完成，用时: {timedelta(seconds=execution_time)}")
            return results
            
        except Exception as e:
            logger.error(f"回测执行失败: {e}")
            raise
    
    async def _analyze_signals_concurrent(self, filtered_token_data: Dict[str, Any], 
                                        max_concurrent_tokens: int = 10) -> List[Dict[str, Any]]:
        """并发分析多个token的信号，提升性能
        
        Args:
            filtered_token_data: 过滤后的token数据
            max_concurrent_tokens: 最大并发token数量
            
        Returns:
            List[Dict[str, Any]]: 所有买入信号列表
        """
        token_items = list(filtered_token_data.items())
        
        # 创建信号量控制并发数量
        semaphore = asyncio.Semaphore(max_concurrent_tokens)
        
        async def analyze_single_token(token_address: str, token_data: Dict[str, Any]) -> List[Dict[str, Any]]:
            """分析单个token的信号"""
            async with semaphore:
                logger.debug(f"分析token {token_address} 的信号")
                return self.signal_analyzer.analyze_token_signals(token_address, token_data)
        
        # 并发执行所有token的信号分析
        tasks = [
            analyze_single_token(token_address, token_data) 
            for token_address, token_data in token_items
        ]
        
        # 等待所有任务完成并收集结果
        token_signals_list = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 合并所有token的信号结果
        all_buy_signals = []
        for i, signals in enumerate(token_signals_list):
            if isinstance(signals, Exception):
                token_address = token_items[i][0]
                logger.error(f"Token {token_address} 信号分析失败: {signals}")
                continue
            if signals:  # 如果该token有信号
                all_buy_signals.extend(signals)
        
        return all_buy_signals
    
    def _calculate_trades(self, buy_signals: List[Dict[str, Any]], 
                         sell_signals: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """根据买入卖出信号计算具体交易
        
        Args:
            buy_signals: 买入信号列表
            sell_signals: 卖出信号列表
            
        Returns:
            List[Dict[str, Any]]: 交易列表
        """
        logger.info(f"计算 {len(buy_signals)} 个买入信号对应的交易")
        trades = []
        
        for buy_signal in buy_signals:
            # 查找对应的卖出信号
            sell_signal = next(
                (s for s in sell_signals if s['token_address'] == buy_signal['token_address'] 
                 and s['buy_signal_timestamp'] == buy_signal['signal_timestamp']), 
                None
            )
            
            if sell_signal:
                trade = {
                    'token_address': buy_signal['token_address'],
                    'buy_timestamp': buy_signal['signal_timestamp'],
                    'sell_timestamp': sell_signal['sell_timestamp'],
                    'buy_price': buy_signal.get('avg_price_usd', 0),
                    'sell_price': sell_signal.get('avg_price_usd', 0),
                    'quantity': buy_signal.get('total_volume_usd', 0),
                    'buy_reason': 'kol_signal',
                    'sell_reason': sell_signal.get('sell_reason', 'unknown'),
                    'kol_count': buy_signal.get('trigger_kol_count', 0),
                    'holding_hours': (sell_signal['sell_timestamp'] - buy_signal['signal_timestamp']) / 60
                }
                
                # 计算收益率（考虑手续费和滑点）
                commission = self.config.commission_pct
                slippage = self.config.slippage_pct
                
                if trade['buy_price'] > 0 and trade['sell_price'] > 0:
                    effective_buy_price = trade['buy_price'] * (1 + commission + slippage)
                    effective_sell_price = trade['sell_price'] * (1 - commission - slippage)
                    
                    trade['return_rate'] = (effective_sell_price - effective_buy_price) / effective_buy_price
                    trade['profit_usd'] = trade['quantity'] * trade['return_rate']
                else:
                    # 价格数据不可用时，使用模拟收益
                    trade['return_rate'] = 0.0
                    trade['profit_usd'] = 0.0
                    trade['price_data_available'] = False
                
                trades.append(trade)
            else:
                logger.warning(f"买入信号 {buy_signal['token_address']}:{buy_signal['signal_timestamp']} "
                             f"未找到对应卖出信号")
        
        logger.info(f"交易计算完成，生成 {len(trades)} 笔交易")
        return trades
