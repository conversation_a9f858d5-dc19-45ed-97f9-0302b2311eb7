"""配置管理组件 - 回测模块V2

统一管理所有配置参数，支持多种配置来源，提供参数验证和默认值处理。
"""

import json
import yaml
import logging
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
import itertools

logger = logging.getLogger("ConfigManagerV2")


@dataclass
class BacktestConfigV2:
    """回测模块V2配置类"""
    
    # 回测时间参数
    backtest_start_time: int = **********  # 2023-01-01 00:00:00 UTC
    backtest_end_time: int = **********    # 2023-02-01 00:00:00 UTC
    
    # 数据筛选参数
    transaction_min_amount: float = 500.0  # 最小交易金额(USD)
    kol_account_min_txs: int = 10          # KOL账号最小交易数
    kol_account_max_txs: int = 500         # KOL账号最大交易数
    kol_account_min_count: int = 6         # 最小KOL账号数量
    kol_min_winrate: float = 0.0           # KOL最小总胜率阈值(0-1，默认0表示不过滤)
    token_mint_lookback_hours: int = 48    # 代币创建时间回溯小时数
    
    # 策略参数
    transaction_lookback_hours: int = 24   # 交易回溯时间窗口(小时)
    sell_strategy_hours: int = 24          # 卖出策略时间窗口(小时)
    sell_kol_ratio: float = 0.5            # 卖出KOL比例阈值
    
    # 回测控制参数
    fixed_trade_amount: float = 100.0      # 固定买入金额(USD)
    commission_pct: float = 0.003          # 手续费百分比
    slippage_pct: float = 0.002            # 滑点百分比
    same_token_notification_interval_minutes: int = 60  # 相同代币通知最小间隔(分钟)
    
    def __post_init__(self):
        """后处理初始化，验证参数有效性"""
        self.validate()
    
    def validate(self):
        """验证配置参数的有效性"""
        if self.backtest_start_time >= self.backtest_end_time:
            raise ValueError("回测开始时间必须小于结束时间")
        
        if self.transaction_min_amount <= 0:
            raise ValueError("最小交易金额必须大于0")
        
        if self.kol_account_min_count <= 0:
            raise ValueError("最小KOL账号数量必须大于0")
        
        if self.kol_account_min_txs <= 0:
            raise ValueError("KOL最小交易数必须大于0")
        
        if self.kol_account_max_txs < self.kol_account_min_txs:
            raise ValueError("KOL最大交易数必须大于等于最小交易数")
        
        if not 0 < self.sell_kol_ratio <= 1:
            raise ValueError("卖出KOL比例阈值必须在(0, 1]范围内")

        if not 0 <= self.kol_min_winrate <= 1:
            raise ValueError("KOL最小总胜率阈值必须在[0, 1]范围内")

        if self.transaction_lookback_hours <= 0:
            raise ValueError("交易回溯时间窗口必须大于0")

        if self.sell_strategy_hours <= 0:
            raise ValueError("卖出策略时间窗口必须大于0")

        if self.token_mint_lookback_hours <= 0:
            raise ValueError("代币创建时间回溯小时数必须大于0")
        
        if self.same_token_notification_interval_minutes <= 0:
            raise ValueError("相同代币通知最小间隔必须大于0")
        
        if self.fixed_trade_amount <= 0:
            raise ValueError("固定买入金额必须大于0")


class ConfigManagerV2:
    """配置管理器V2"""
    
    @staticmethod
    def load_from_file(file_path: str) -> BacktestConfigV2:
        """从文件加载配置

        Args:
            file_path: 配置文件路径，支持JSON和YAML

        Returns:
            BacktestConfigV2: 配置对象
        """
        try:
            if file_path.endswith('.json'):
                with open(file_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
            elif file_path.endswith(('.yaml', '.yml')):
                with open(file_path, 'r', encoding='utf-8') as f:
                    config_data = yaml.safe_load(f)
            else:
                raise ValueError("不支持的配置文件格式，请使用JSON或YAML")

            # 过滤掉V2不支持的参数
            filtered_config_data = ConfigManagerV2._filter_v2_params(config_data)

            return BacktestConfigV2(**filtered_config_data)
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            raise

    @staticmethod
    def _filter_v2_params(config_data: Dict[str, Any]) -> Dict[str, Any]:
        """过滤出V2支持的参数

        Args:
            config_data: 原始配置数据

        Returns:
            Dict[str, Any]: 过滤后的配置数据
        """
        # 获取BacktestConfigV2支持的所有字段
        import inspect
        v2_fields = set(inspect.signature(BacktestConfigV2.__init__).parameters.keys())
        v2_fields.discard('self')  # 移除self参数

        # 过滤配置数据
        filtered_data = {}
        for key, value in config_data.items():
            if key in v2_fields:
                filtered_data[key] = value
            else:
                logger.warning(f"忽略V2不支持的配置参数: {key} = {value}")

        return filtered_data
    
    @staticmethod
    def get_default_config() -> BacktestConfigV2:
        """返回默认配置参数
        
        Returns:
            BacktestConfigV2: 默认配置对象
        """
        return BacktestConfigV2()
    
    @staticmethod
    def apply_cli_overrides(config: BacktestConfigV2, args) -> BacktestConfigV2:
        """应用命令行参数覆盖
        
        Args:
            config: 基础配置对象
            args: 命令行参数对象
            
        Returns:
            BacktestConfigV2: 更新后的配置对象
        """
        # 将配置对象转换为字典
        config_dict = config.__dict__.copy()
        
        # 应用命令行参数覆盖
        for key, value in vars(args).items():
            if value is not None and hasattr(config, key):
                config_dict[key] = value
                logger.info(f"命令行参数覆盖: {key} = {value}")
        
        # 创建新的配置对象
        return BacktestConfigV2(**config_dict)
    
    @staticmethod
    def generate_parameter_combinations(param_grid: Dict[str, List]) -> List[Dict[str, Any]]:
        """生成参数网格的所有组合
        
        Args:
            param_grid: 参数网格，如 {'param1': [1, 2], 'param2': [3, 4]}
            
        Returns:
            List[Dict[str, Any]]: 参数组合列表
        """
        # 获取所有参数名和可能的值
        param_names = list(param_grid.keys())
        param_values = list(param_grid.values())
        
        # 生成笛卡尔积
        combinations = list(itertools.product(*param_values))
        
        # 转换为字典列表
        result = []
        for combination in combinations:
            config_dict = {name: value for name, value in zip(param_names, combination)}
            result.append(config_dict)
        
        logger.info(f"生成了 {len(result)} 个参数组合")
        return result
    
    @staticmethod
    def validate_config(config: Dict[str, Any]) -> bool:
        """验证配置参数的有效性
        
        Args:
            config: 配置字典
            
        Returns:
            bool: 配置是否有效
        """
        try:
            # 尝试创建配置对象，会自动验证
            BacktestConfigV2(**config)
            return True
        except Exception as e:
            logger.error(f"配置验证失败: {e}")
            return False
