# smart_money包 
from abc import abstractmethod
from dataclasses import dataclass
from enum import Enum
import os
import random
import asyncio
import time
import traceback
from typing import List, Optional, Union

from fake_useragent import UserAgent
from utils.session import AsyncProxySession
from curl_cffi.requests import Response
import logging

from utils.proxy.proxy_poll import FreeProxy, PaidProxy, ProxyPoll, QgNetProxyPoll


@dataclass
class Proxys:
    free_proxy: List[FreeProxy]
    paid_proxy: List[PaidProxy]
    

class ProxyType(Enum):
    FREE = "free"
    PAID = "paid"


PROXYs: Proxys = Proxys(
    free_proxy=[ProxyPoll(os.getenv("FREE_PROXY_URL"))],
    paid_proxy=[QgNetProxyPoll()],
)


class BasicSpider:
    # 类变量，用于存储UserAgent实例
    _user_agent: Union[UserAgent, List[str], None] = None
    
    def __init__(self, max_retries: int = 5, retry_interval: float = 1.0):
        """
        初始化爬虫
        
        Args:
            max_retries: 最大重试次数
            retry_interval: 重试间隔时间(秒)
        """
        self.session = None
        self.max_retries = max_retries
        self.retry_interval = retry_interval
        self.logger = logging.getLogger(self.__class__.__name__)
        
    @classmethod
    def get_user_agent_instance(cls) -> Union[UserAgent, List[str]]:
        """获取UserAgent实例（单例模式）"""
        if cls._user_agent is None:
            try:
                cls._user_agent = UserAgent()
            except Exception as e:
                cls.logger.warning(f"创建UserAgent实例失败: {e}，使用备用User-Agent列表")
                # 备用User-Agent列表
                cls._user_agent = [
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36",
                    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:123.0) Gecko/20100101 Firefox/123.0",
                    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3 Safari/605.1.15",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Edge/121.0.0.0",
                ]
        return cls._user_agent

    async def get_random_user_agent(self) -> str:
        """获取随机User-Agent"""
        ua = self.get_user_agent_instance()
        if isinstance(ua, list):
            return random.choice(ua)
        try:
            return ua.random
        except Exception as e:
            self.logger.warning(f"获取随机User-Agent失败: {e}，使用备用列表")
            self.__class__._user_agent = None  # 重置实例，下次重新创建
            return self.get_random_user_agent()
    
    async def init_sessions(self):
        """初始化会话，向后兼容"""
        if not hasattr(self, 'session') or self.session is None:
            await self.switch_session()
    
    async def get_random_session(self, proxy_type: ProxyType = ProxyType.PAID):
        """获取一个随机的代理会话，但不管理旧会话的关闭"""
        proxy = random.choice(PROXYs.free_proxy) if proxy_type == ProxyType.FREE else random.choice(PROXYs.paid_proxy)
        proxy_info = await proxy.get_proxy()
        self.logger.info(proxy_info)
        
        # 如果headers不存在，则生成一个随机请求头
        # 如果存在，则更新请求头
        if not hasattr(self, 'headers'):
            headers = await self.get_random_headers()
        else:
            headers = self.headers.update(await self.get_random_headers())
        
        return AsyncProxySession(
            # proxy_info.proxy_url, 
            # proxy_info.proxy_user, 
            # proxy_info.proxy_pass,
            headers=headers,
        )
        
    async def switch_session(self, proxy_type: ProxyType = ProxyType.PAID):
        """切换到新的会话，自动关闭旧会话"""
        # 创建新会话
        new_session = await self.get_random_session(proxy_type)
        
        # 关闭旧会话
        if hasattr(self, 'session') and self.session:
            try:
                await self.session.close()
            except TypeError as te: # Catch the specific TypeError
                # Log the specific error if the handle is None
                if "cdata pointer, not NoneType" in str(te):
                    self.logger.warning(f"尝试关闭旧会话时句柄已失效 (None): {te}")
                else:
                    # Log other TypeErrors if they occur
                    self.logger.error(f"关闭旧会话时发生TypeError: {te}")
                    self.logger.debug(traceback.format_exc()) # Log traceback for debugging
            except Exception as close_error:
                # Catch any other potential errors during close
                self.logger.error(f"关闭旧会话时出错: {close_error}")
                self.logger.debug(traceback.format_exc()) # Log traceback for debugging

        # 更新当前会话
        self.session = new_session
        return self.session
        
    @abstractmethod
    def setup(self):
        """设置会话"""
        pass
    
    async def get_random_headers(self) -> dict:
        """生成随机请求头，适用于API请求"""
        ua = await self.get_random_user_agent()
        
        # 为API请求优化的请求头
        headers = {
            "user-agent": ua,
            "accept": "application/json, text/plain, */*",
            "accept-language": random.choice(["zh-CN,zh;q=0.9,en;q=0.8", "en-US,en;q=0.9", "zh-TW,zh;q=0.9,en;q=0.8"]),
            "accept-encoding": "gzip, deflate, br",
            "connection": random.choice(["keep-alive", "close"]),
            "cache-control": random.choice(["no-cache", "max-age=0"]),
        }
        
        # 随机添加一些API常见的请求头
        # if random.random() < 0.5:
            # headers["content-type"] = "application/json; charset=utf-8"
        
        # if random.random() < 0.3:
            # headers["origin"] = "https://app.example.com"
            
        # if random.random() < 0.4:
            # headers["x-requested-with"] = "XMLHttpRequest"
            
        # 随机设置一个合理的客户端时间戳
        # if random.random() < 0.6:
            # headers["x-timestamp"] = str(int(time.time() * 1000))
            
        # 随机客户端版本号
        # if random.random() < 0.4:
            # version = f"{random.randint(1,5)}.{random.randint(0,9)}.{random.randint(0,9)}"
            # headers["x-app-version"] = version
            
        return headers
        
    async def request_with_retry(self, method: str, url: str, retry_interval: Optional[float] = None, **kwargs) -> Response:
        """
        发送请求，遇到错误时自动重试
        
        Args:
            method: 请求方法，如'get', 'post'等
            url: 请求URL
            retry_interval: 重试间隔时间(秒)，如果为None则使用实例的默认值
            **kwargs: 传递给session.request的其他参数
            
        Returns:
            Response对象
        """
        retries = 0
        interval = retry_interval if retry_interval is not None else self.retry_interval
        last_error = None
        response = None
        
        while retries < self.max_retries:
            try:
                # 初始化会话或在重试时切换到新会话
                if retries == 0 and not hasattr(self, 'session'):
                    await self.init_sessions()
                elif retries > 0:
                    await self.switch_session(proxy_type=ProxyType.PAID)
                    self.logger.info(f"切换到新代理，第{retries+1}次重试")
                
                await self.setup()
                
                response = await self.session.request(method, url, **kwargs)
                # 检查响应状态码
                if 200 <= response.status_code < 300:
                    return response
                
                self.logger.info(f"请求返回状态码 {response.status_code}，第{retries+1}次重试")
                
            except Exception as e:
                last_error = e
                self.logger.info(f"请求出错: {traceback.format_exc()}, 第{retries+1}次重试")
            
            retries += 1
            
            # 如果还有下一次重试且间隔时间大于0，则等待
            if retries < self.max_retries and interval > 0:
                self.logger.info(f"等待 {interval * (retries)} 秒后重试...")
                await asyncio.sleep(interval * retries)
        
        # 如果所有重试都失败
        if response:
            return response
        
        # 如果没有响应对象但有错误，则抛出最后一个错误
        if last_error:
            raise last_error
            
        # 创建一个空响应（理论上不会执行到这里）
        raise Exception("所有重试都失败，且没有响应或错误")
    
    async def get(self, url: str, retry_interval: Optional[float] = None, **kwargs) -> Response:
        """使用GET方法发送请求，支持自动重试"""
        return await self.request_with_retry('GET', url, retry_interval=retry_interval, **kwargs)
    
    async def post(self, url: str, retry_interval: Optional[float] = None, **kwargs) -> Response:
        """使用POST方法发送请求，支持自动重试"""
        return await self.request_with_retry('POST', url, retry_interval=retry_interval, **kwargs)
    
    async def put(self, url: str, retry_interval: Optional[float] = None, **kwargs) -> Response:
        """使用PUT方法发送请求，支持自动重试"""
        return await self.request_with_retry('PUT', url, retry_interval=retry_interval, **kwargs)
    
    async def delete(self, url: str, retry_interval: Optional[float] = None, **kwargs) -> Response:
        """使用DELETE方法发送请求，支持自动重试"""
        return await self.request_with_retry('DELETE', url, retry_interval=retry_interval, **kwargs)
        
    async def close(self):
        """关闭所有会话连接"""
        if self.session:
            await self.session.close()
            
    async def __aenter__(self):
        """实现异步上下文管理器的进入方法"""
        await self.init_sessions()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """实现异步上下文管理器的退出方法，自动关闭会话资源"""
        await self.close()
        return False  # 返回False表示不抑制异常
