import json
import os
import logging
from typing import Dict, Any, List, Tuple

import pandas as pd
import numpy as np # Keep numpy for potential NaN handling
# Note: Matplotlib and Seaborn are no longer needed if only using ECharts
# import matplotlib.pyplot as plt
# import seaborn as sns
from jinja2 import Environment, FileSystemLoader, exceptions, select_autoescape # Corrected Jinja2 imports
# import base64 # No longer needed if not embedding matplotlib plots
# from io import BytesIO # No longer needed
from datetime import datetime

logger = logging.getLogger("ReportGenerator")
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# 参数描述映射
PARAM_DESCRIPTIONS = {
    "transaction_lookback_hours": "交易回溯小时数",
    "transaction_min_amount": "最低交易金额",
    "kol_account_min_count": "最低KOL账号数",
    "kol_account_min_txs": "每个KOL账号最低交易数",
    "kol_account_max_txs": "每个KOL账号最高交易数",
    "kol_min_winrate_7d": "KOL最低7日胜率",
    "token_mint_lookback_hours": "代币创建回溯小时数",
    "sell_strategy_hours": "卖出策略回溯小时数",
    "sell_kol_ratio": "卖出KOL比例阈值",
    "initial_capital": "初始资金",
    "same_token_notification_interval_minutes": "同代币通知间隔(分钟)",
    # 添加统计指标的中文描述
    "return_rate": "整体收益率",
    "win_rate": "胜率",
    "kelly_fraction_calculated": "凯利分数",
    "max_drawdown": "最大回撤",
    "max_profit": "最大盈利",
    "max_profit_per_trade": "单笔最大收益",
    "total_trades": "总交易数",
    "profit_factor": "盈利因子",
    "sharpe_ratio": "夏普比率",
    "sortino_ratio": "索提诺比率",
    "execution_time_seconds": "执行时间(秒)",
    # 新增中文描述
    "positions_count": "持仓数量",
    "total_realized_pnl": "总已实现盈亏",
    "total_unrealized_pnl": "总未实现盈亏",
    "total_invested": "总投入资金",
    "final_value": "最终价值",
    # 新增
    "winning_trades": "盈利交易数",
    "losing_trades": "亏损交易数",
    # 收益相关指标
    "std_return": "收益率标准差",
    "avg_winning_return": "平均盈利收益率",
    "avg_losing_return": "平均亏损收益率",
    # 持仓时间指标（分钟）
    "avg_holding_minutes": "平均持仓时间(分钟)",
    "max_holding_minutes": "最大持仓时间(分钟)", 
    "min_holding_minutes": "最小持仓时间(分钟)",
    # 分组统计
    "sell_reason_stats": "卖出原因统计",
    "kol_count_stats": "KOL数量统计",
    # 时间相关
    "backtest_start_time": "回测开始时间",
    "backtest_end_time": "回测结束时间",
    "backtest_days": "回测天数",
    # 新增统计指标
    "profit_loss_ratio": "盈亏比",
    # 交易相关参数
    "fixed_trade_amount": "固定交易金额",
    "total_profit_usd": "总盈利(USD)",
    "total_loss_usd": "总亏损(USD)",
    "net_profit_usd": "净盈利(USD)",
    "max_position_size": "最大持仓规模",
    "min_position_size": "最小持仓规模",
    "avg_position_size": "平均持仓规模",
    "total_commission_usd": "总手续费(USD)",
    "avg_commission_per_trade": "平均每笔手续费",
    # 时间相关
    "execution_time": "执行时间",
    "processing_time": "处理时间",
    "total_runtime": "总运行时间",
    # 资金相关
    "total_capital": "总资金",
    "available_capital": "可用资金",
    "used_capital": "已用资金",
    "capital_utilization": "资金利用率",
    # 风险相关
    "max_risk_per_trade": "每笔最大风险",
    "risk_reward_ratio": "风险回报比",
    "volatility": "波动率",
    # 其他常见参数
    "strategy_type": "策略类型",
    "market_type": "市场类型",
    "timeframe": "时间框架",
    "commission_rate": "手续费率",
    "slippage_rate": "滑点率",
    "slippage_pct": "滑点百分比",
    "commission_pct": "手续费百分比"
    # 可以根据需要添加更多参数描述
}

HIDDEN_PARAMS = ['backtest_start_time', 'backtest_end_time', 'use_real_price', 'skip_price_api_query', 'processing_interval', 'index', 'param_index']

# --- 单次运行报告模板 ---
SINGLE_RUN_HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单次回测运行详情 - 参数组合 {{ param_index }}</title>
    <style>
        body { font-family: sans-serif; margin: 20px; }
        h1, h2 { color: #333; }
        table { border-collapse: collapse; width: 100%; margin-bottom: 20px; font-size: 0.9em; }
        th, td { border: 1px solid #ddd; padding: 6px; text-align: left; }
        th { background-color: #f2f2f2; }
        .config-section, .stats-section, .trades-section { margin-bottom: 30px; }
        .config-list, .stats-list { list-style: none; padding-left: 0; }
        .config-list li, .stats-list li { margin-bottom: 5px; }
        .code { background-color: #eee; padding: 2px 4px; border-radius: 3px; font-family: monospace; }
        .metric-value { font-weight: bold; }
        .metric-positive { color: green; }
        .metric-negative { color: red; }
        .metric-neutral { color: orange; }
        .metric-na { color: grey; }
        .trade-buy td { background-color: #e6ffe6; } /* Light green for buy */
        .trade-sell td { background-color: #ffe6e6; } /* Light red for sell */
    </style>
</head>
<body>
    <h1>单次回测运行详情 - 参数组合 {{ param_index }}</h1>
    <p>报告生成时间: {{ generation_time }}</p>
    <p>JSON源文件: <code class="code">{{ json_file_path }}</code></p>

    <div class="config-section">
        <h2>参数配置</h2>
        <ul class="config-list">
            {% for key, value in config.items() %}
                <li><code class="code">{{ param_descriptions.get(key, key) }}</code> ({{ key }}): {{ value }}</li>
            {% endfor %}
        </ul>
    </div>

    <div class="stats-section">
        <h2>统计摘要</h2>
         <ul class="stats-list">
            {% for key, value in statistics.items() %}
                 <li><strong>{{ param_descriptions.get(key, key) }}:</strong> 
                 {% if key in ['return_rate', 'win_rate', 'max_drawdown', 'max_profit_per_trade', 'sharpe_ratio', 'sortino_ratio', 'profit_factor', 'kelly_fraction_calculated', 'avg_winning_return', 'avg_losing_return'] %}
                     {# Apply coloring based on metric nature #}
                     {% set val_float = value | float(default=0.0) %}
                     <span class="metric-value 
                         {% if key == 'return_rate' and val_float > 0 %}metric-positive
                         {% elif key == 'return_rate' and val_float < 0 %}metric-negative
                         {% elif key == 'win_rate' and val_float > 0.5 %}metric-positive
                         {% elif key == 'win_rate' and val_float >= 0 %}metric-neutral
                         {% elif key == 'max_drawdown' and val_float > 0 %}metric-negative {# Drawdown is negative by nature, display abs #}
                         {% elif key == 'max_profit_per_trade' and val_float > 0 %}metric-positive
                         {% elif key in ['sharpe_ratio', 'sortino_ratio', 'profit_factor'] and val_float > 1 %}metric-positive
                         {% elif key in ['sharpe_ratio', 'sortino_ratio', 'profit_factor'] and val_float > 0 %}metric-neutral
                         {% elif key == 'kelly_fraction_calculated' and val_float > 0.1 %}metric-positive
                         {% elif key == 'kelly_fraction_calculated' and val_float > 0 %}metric-neutral 
                         {% elif value is none %}metric-na
                         {% else %}metric-neutral {# Default/other cases #}
                         {% endif %}
                     ">
                     {# Format specific metrics #}
                     {% if key in ['return_rate', 'win_rate', 'avg_winning_return', 'avg_losing_return'] %}
                         {{ "%.2f%%" | format(val_float * 100) }}
                     {% elif key == 'max_drawdown' %}
                         {{ "%.2f%%" | format((value | abs) * 100) if value is not none else 'N/A' }}
                     {% elif key in ['kelly_fraction_calculated', 'max_profit_per_trade'] %}
                         {{ "%.4f" | format(val_float) if value is not none else 'N/A' }}
                      {% elif key in ['total_invested', 'final_value', 'total_realized_pnl'] %}
                         {{ "$%.2f" | format(val_float) }}
                     {% elif key in ['sharpe_ratio', 'sortino_ratio', 'profit_factor'] %}
                          {{ "%.2f" | format(val_float) if value is not none else 'N/A' }}
                     {% else %}
                         {{ value if value is not none else 'N/A' }}
                     {% endif %}
                     </span>
                 {% else %}
                     {{ value }}
                 {% endif %}
                 </li>
            {% endfor %}
        </ul>
    </div>

    <div class="trades-section">
        <h2>交易记录 ({{ trades | length }} 条)</h2>
        {% if trades %}
        <table>
            <thead>
                <tr>
                    <th>时间戳</th>
                    <th>交易类型</th>
                    <th>代币地址</th>
                    <th>数量</th>
                    <th>价格</th>
                    <th>成本/收益 ($)</th>
                    <th>手续费 ($)</th>
                    <th>已实现 PnL ($)</th>
                    <th>持有成本 ($)</th>
                    <th>事件来源</th>
                </tr>
            </thead>
            <tbody>
                {% for trade in trades %}
                <tr class="trade-{{ trade.type | lower }}">
                    <td>{{ trade.timestamp }}</td>
                    <td>{{ trade.type }}</td>
                    <td><code class="code">{{ trade.token_address }}</code></td>
                    <td>{{ "%.6f" | format(trade.quantity) }}</td>
                    <td>{{ "%.6f" | format(trade.price) }}</td>
                    <td>{{ "%.2f" | format(trade.cost_usd) }}</td>
                    <td>{{ "%.4f" | format(trade.commission_usd) }}</td>
                    <td>{{ "%.2f" | format(trade.realized_pnl) if trade.realized_pnl is not none else '' }}</td>
                    <td>{{ "%.2f" | format(trade.cost_basis) if trade.cost_basis is not none else '' }}</td>
                    <td>{{ trade.event_type }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% else %}
        <p>本次运行没有交易记录。</p>
        {% endif %}
    </div>

</body>
</html>
"""

# --- Function to Prepare ECharts Data ---
def _prepare_echarts_data(df: pd.DataFrame, metrics_info: Dict[str, str], top_n: int) -> Tuple[str, str, str]:
    """处理 DataFrame 并为 ECharts 准备数据结构。

    Args:
        df (pd.DataFrame): 包含已处理回测结果的 DataFrame（应包含数值数据）。
        metrics_info (Dict[str, str]): 将指标键映射到条形图显示名称的字典。
        top_n (int): 要考虑的条形图顶部结果数。

    Returns:
        Tuple[str, str, str]: 散点图数据、按收益率排名的顶部条形图数据、按胜率排名的顶部条形图数据的 JSON 字符串。
    """
    logger.info("进入 _prepare_echarts_data...") # 添加日志
    # === 修改开始: 使用带 'statistics_' 前缀的列名 ===
    win_rate_col = 'statistics_win_rate'
    return_rate_col = 'statistics_return_rate'
    kelly_col = 'statistics_kelly_fraction_calculated'
    param_index_col = 'param_index'
    total_trades_col = 'statistics_total_trades'
    
    # 添加日志：检查转换前的数据
    logger.info("检查数值转换前的数据:")
    if win_rate_col in df.columns:
        logger.info(f"  列 '{win_rate_col}' (前5行):\n{df[win_rate_col].head().to_string()}")
        logger.info(f"  列 '{win_rate_col}' 数据类型: {df[win_rate_col].dtype}")
    else:
        logger.warning(f"  列 '{win_rate_col}' 不存在。")
    if return_rate_col in df.columns:
        logger.info(f"  列 '{return_rate_col}' (前5行):\n{df[return_rate_col].head().to_string()}")
        logger.info(f"  列 '{return_rate_col}' 数据类型: {df[return_rate_col].dtype}")
    else:
         logger.warning(f"  列 '{return_rate_col}' 不存在。")
            
    # 确保计算/绘图所需的数值类型
    numeric_cols_with_prefix = ['statistics_' + k for k in metrics_info.keys()] + [
        total_trades_col, win_rate_col, return_rate_col, kelly_col # 使用定义的变量
    ]
    # === 修改结束 ===

    for col in numeric_cols_with_prefix:
        if col in df.columns:
            # 记录转换前的类型
            # original_dtype = df[col].dtype 
            df[col] = pd.to_numeric(df[col], errors='coerce')
            # if original_dtype != df[col].dtype:
            #     logger.info(f"列 '{col}' 类型从 {original_dtype} 转换为 {df[col].dtype}")
        else:
            df[col] = np.nan
            logger.warning(f"列 '{col}' 在 DataFrame 中不存在，已创建为 NaN 列。")

    # 添加日志：检查转换后的数据
    logger.info("检查数值转换后的数据:")
    if win_rate_col in df.columns:
        logger.info(f"  列 '{win_rate_col}' (前5行):\n{df[win_rate_col].head().to_string()}")
    if return_rate_col in df.columns:
        logger.info(f"  列 '{return_rate_col}' (前5行):\n{df[return_rate_col].head().to_string()}")
        
    # 确保 param_index 存在
    if param_index_col not in df.columns: # 使用定义的变量
        try:
            df[param_index_col] = df.index.astype(int) + 1
        except (TypeError, ValueError):
            df[param_index_col] = range(1, len(df) + 1)

    # 1. 散点图数据
    scatter_list = []
    if win_rate_col in df.columns and return_rate_col in df.columns:
        scatter_df = df[[win_rate_col, return_rate_col, kelly_col, param_index_col, total_trades_col]].copy()
        scatter_df.replace([np.inf, -np.inf], np.nan, inplace=True)

        scatter_list = [
            [
                row.get(win_rate_col),
                row.get(return_rate_col),
                row.get(kelly_col),
                int(row.get(param_index_col)) if pd.notna(row.get(param_index_col)) else None,
                row.get(total_trades_col)
            ]
            for index, row in scatter_df.iterrows()
            if pd.notna(row.get(win_rate_col)) and pd.notna(row.get(return_rate_col))
        ]
        
    # 添加日志：检查生成的 scatter_list 长度
    logger.info(f"生成的 scatter_list 长度: {len(scatter_list)}")
    if len(scatter_list) == 0 and not df.empty:
         logger.warning("scatter_list 为空。可能是所有行的 win_rate 或 return_rate 都是无效值(NaN)。")
         # 可以选择性地打印一些被过滤掉的行的数据
         # logger.info("被过滤掉的行的 win_rate/return_rate (前5个NaN):")
         # filtered_out = df[~(pd.notna(df[win_rate_col]) & pd.notna(df[return_rate_col]))]
         # logger.info(filtered_out[[win_rate_col, return_rate_col]].head().to_string())


    # 辅助函数：将 NaN 转换为 None 以便 JSON 序列化
    def nan_to_null(obj):
        if isinstance(obj, dict):
            return {k: nan_to_null(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [nan_to_null(elem) for elem in obj]
        elif isinstance(obj, float) and np.isnan(obj):
            return None
        elif pd.isna(obj):
            return None
        elif isinstance(obj, np.int64):
             return int(obj)
        return obj

    scatter_data_json = json.dumps(nan_to_null(scatter_list))

    # 2. 条形图数据（按收益率排名前 N）
    top_by_return = df.nlargest(min(top_n, len(df)), return_rate_col) # 使用带前缀的列名
    bar_return_data = _prepare_bar_data(top_by_return, metrics_info)
    bar_return_data_json = json.dumps(nan_to_null(bar_return_data))

    # 3. 条形图数据（按胜率排名前 N）
    top_by_winrate = df.nlargest(min(top_n, len(df)), win_rate_col) # 使用带前缀的列名
    bar_winrate_data = _prepare_bar_data(top_by_winrate, metrics_info)
    bar_winrate_data_json = json.dumps(nan_to_null(bar_winrate_data))

    return scatter_data_json, bar_return_data_json, bar_winrate_data_json

def _prepare_bar_data(df_subset: pd.DataFrame, metrics_info: Dict[str, str]) -> Dict[str, Any]:
    """为 ECharts 条形图准备数据结构。

    Args:
        df_subset (pd.DataFrame): 包含要绘制的参数组合数据的子集。
        metrics_info (Dict[str, str]): 将指标键映射到条形图显示名称的字典。

    Returns:
        Dict[str, Any]: 包含 ECharts 条形图所需 categories, legend, series 的字典。
    """
    # === 修改开始: 使用带 'statistics_' 前缀的列名 ===
    if df_subset.empty:
        return {"categories": [], "legend": [], "series": []}

    categories = [f"组合 {idx}" for idx in df_subset['param_index']] # 使用 param_index
    legend = list(metrics_info.values()) # 使用中文名作为图例
    series = []

    for metric_key, metric_name in metrics_info.items():
        # 构造带前缀的列名
        prefixed_metric_key = f'statistics_{metric_key}'
        if prefixed_metric_key in df_subset.columns:
            # 格式化数据，确保是列表，并处理 NaN/None
            metric_values = df_subset[prefixed_metric_key].apply(
                lambda x: f'{x*100:.2f}' if metric_key in ['win_rate', 'return_rate'] and pd.notna(x) else (f'{x:.4f}' if pd.notna(x) else None)
            ).tolist()
            
            # 将格式化后的 None (原 NaN) 替换为 0 或其他合适的值，以便图表显示
            # 或者保留 None 让 ECharts 处理空值
            # cleaned_values = [float(v) if v is not None else 0 for v in metric_values] # Example: Replace None with 0
            cleaned_values = [float(v) if v is not None else None for v in metric_values] # Keep None for ECharts

            series.append({
                "name": metric_name,
                "data": cleaned_values
            })
        else:
             logger.warning(f"条形图的指标列 '{prefixed_metric_key}' 在 DataFrame 中不存在，跳过。")
    # === 修改结束 ===

    return {
        "categories": categories,
        "legend": legend,
        "series": series
    }

# --- Main function ---
def generate_html_report(json_file_path: str, output_html_path: str, results_data: List[Dict] = None):
    """生成包含统计数据和 ECharts 图表的 HTML 回测报告。

    Args:
        json_file_path (str): param_search_results.json 文件的路径。
        output_html_path (str): 要保存的 HTML 报告的路径。
        results_data (List[Dict], optional): 如果提供了，直接使用此数据而不是读取文件。
    """
    logger.info(f"开始生成 HTML 报告: {output_html_path}")

    try:
        if results_data is None:
            logger.info(f"从文件加载结果: {json_file_path}")
            with open(json_file_path, 'r') as f:
                data = json.load(f)
            results_data = data['results'] # 获取 'results' 列表
            time_stats = data.get('time_stats', {}) # 获取时间统计
        else:
            logger.info("使用传入的 results_data 生成报告")
            # 如果外部传入了 results_data，我们可能没有 time_stats
            # 尝试从传入数据的结构中获取（如果它包含了 time_stats）
            if isinstance(results_data, dict) and 'results' in results_data and 'time_stats' in results_data:
                time_stats = results_data.get('time_stats', {})
                results_data = results_data['results']
            else:
                time_stats = {} # 如果只传入了列表，则时间统计未知

        if not results_data:
            logger.warning("结果数据为空，无法生成报告。")
            # 可以选择生成一个空的或包含错误信息的报告
            # _write_empty_report(output_html_path, "结果数据为空")
            return

        # === 修改开始: 确保在 json_normalize 之前处理 statistics ===
        # 清理数据：确保 statistics 是字典，如果不是或缺失，则设为空字典
        cleaned_results = []
        for item in results_data:
            if isinstance(item, dict):
                if 'statistics' not in item or not isinstance(item.get('statistics'), dict):
                    item['statistics'] = {}
                cleaned_results.append(item)
            else:
                 logger.warning(f"结果列表中的项目不是字典，已跳过: {item}")
        # === 修改结束 ===
        
        # 使用清理后的数据进行 normalize
        df = pd.json_normalize(cleaned_results, sep='_')

        # --- 数据准备 ---
        # 提取参数列和指标列
        # 修改: 排除内部参数
        internal_params_to_exclude = [
            'backtest_start_time', 'backtest_end_time', 'use_real_price', 
            'skip_price_api_query', 'processing_interval'
        ]
        # 修改: 不再需要单独提取 param_cols 用于表格显示
        # param_cols = [
        #     col for col in df.columns 
        #     if col.startswith('params_') and col.replace('params_', '') not in internal_params_to_exclude
        # ] 
        stat_cols = [col for col in df.columns if col.startswith('statistics_')] 
        other_cols = ['param_index', 'result_dir', 'execution_time', 'single_report_rel_path']
        # 修改: display_cols 不再包含 param_cols，但需要包含新增的 '详细配置' 列 (稍后添加)
        display_cols = ['param_index'] + stat_cols + ['execution_time', 'single_report_rel_path']
        
        # 重命名列以提高可读性 (移除前缀)
        rename_map = {'param_index': '组合编号'}
        # 修改: 只重命名 stat_cols 和其他需要的列
        # for col in param_cols:
        #     rename_map[col] = PARAM_DESCRIPTIONS.get(col.replace('params_', ''), col.replace('params_', ''))
        for col in stat_cols:
            rename_map[col] = PARAM_DESCRIPTIONS.get(col.replace('statistics_', ''), col.replace('statistics_', ''))
        rename_map['execution_time'] = '执行时间(秒)'
        rename_map['single_report_rel_path'] = '详细报告'
        
        # 创建用于显示的 DataFrame (暂时不含 '详细配置')
        df_display = df[[col for col in display_cols if col in df.columns]].copy()
        df_display.rename(columns=rename_map, inplace=True)
        
        # 添加 "详细配置" 列，包含带 data-params 属性的 HTML 元素
        params_hover_content = []
        for index, row in df.iterrows():
            param_idx = row['param_index']
            # 从原始 cleaned_results 中找到对应记录以获取未展平的 params
            original_record = next((item for item in cleaned_results if item.get('param_index') == param_idx), None)
            params_dict = original_record.get('params', {}) if original_record else {}
            # 过滤掉内部参数
            filtered_params = {k: v for k, v in params_dict.items() if k not in internal_params_to_exclude}
            # 转为 JSON 字符串，确保使用双引号，HTML属性值使用单引号
            params_json_str = json.dumps(filtered_params, ensure_ascii=False)
            # 使用 html.escape 对 JSON 字符串进行转义，防止 XSS 和属性中断
            import html
            escaped_params_json = html.escape(params_json_str, quote=True) 
            # 创建带 data-* 属性的 span
            hover_html = f'<span class="params-hover-trigger" data-params=\'{escaped_params_json}\'>[查看]</span>'
            params_hover_content.append(hover_html)
        
        df_display['详细配置'] = params_hover_content
        # 将 '详细配置' 列添加到显示顺序中
        display_cols_final = ['组合编号', '详细配置'] + [rename_map.get(col, col) for col in stat_cols if col in rename_map] + ['执行时间(秒)', '详细报告']
        # 确保只包含实际存在的列
        display_cols_final = [col for col in display_cols_final if col in df_display.columns] 
        df_display = df_display[display_cols_final]

        # 格式化特定列 (保持不变)
        if '执行时间(秒)' in df_display.columns:
             df_display['执行时间(秒)'] = df_display['执行时间(秒)'].round(2)
            
        # 格式化统计指标
        for original_col, display_name in rename_map.items():
            if original_col.startswith('statistics_'):
                metric_key = original_col.replace('statistics_', '')
                if display_name in df_display.columns:
                    if metric_key in ['win_rate', 'return_rate', 'avg_winning_return', 'avg_losing_return']:
                        df_display[display_name] = df_display[display_name].apply(lambda x: f'{x*100:.2f}%' if pd.notna(x) else 'N/A')
                    elif metric_key in ['max_drawdown']:
                        df_display[display_name] = df_display[display_name].apply(lambda x: f'{abs(x)*100:.2f}%' if pd.notna(x) else 'N/A')
                    elif metric_key in ['kelly_fraction_calculated', 'max_profit_per_trade']:
                        df_display[display_name] = df_display[display_name].apply(lambda x: f'{x:.4f}' if pd.notna(x) else 'N/A')
                    elif metric_key in ['sharpe_ratio', 'sortino_ratio', 'profit_factor']:
                         df_display[display_name] = df_display[display_name].apply(lambda x: f'{x:.2f}' if pd.notna(x) else 'N/A')
                    # 保留其他指标的原样或添加更多格式化

        # 将 '详细报告' 列转换为链接
        if '详细报告' in df_display.columns:
            df_display['详细报告'] = df_display['详细报告'].apply(
                lambda x: f'<a href="{x}" target="_blank">查看详情</a>' if pd.notna(x) else 'N/A'
            )

        # --- ECharts 数据准备 ---
        top_n_results = 5 # 要在条形图中显示的最佳结果数
        # 定义条形图要显示的指标及其名称
        metrics_for_bars = {
            'return_rate': '收益率 (%)', 
            'win_rate': '胜率 (%)',
            'kelly_fraction_calculated': '凯利分数',
            'max_drawdown': '最大回撤',
            'max_profit_per_trade': '单笔最大收益',
            'total_trades': '总交易数'
        }
        # 传递原始 DataFrame (df) 给 _prepare_echarts_data
        scatter_data_json, bar_return_data_json, bar_winrate_data_json = _prepare_echarts_data(df.copy(), metrics_for_bars, top_n_results)

        # --- 准备 HTML 表格 ---
        # 确保 '组合编号' 是第一列
        # if '组合编号' in df_display.columns:
        #      cols_ordered = ['组合编号'] + [col for col in df_display.columns if col != '组合编号']
        #      df_display = df_display[cols_ordered]
             
        # 使用新的 df_display 生成 HTML 表格
        results_table_html = df_display.to_html(escape=False, index=False, classes='results-table', border=0, table_id='results-detail-table') 

        # --- 提取最佳结果用于摘要 ---
        def extract_best_data(best_result_row):
            if best_result_row is None or best_result_row.empty:
                return None
            
            # 从 Series 转换为字典，并处理 NaN
            data = best_result_row.iloc[0].where(pd.notna(best_result_row.iloc[0]), None).to_dict()
            
            # 从原始 DataFrame 'df' 中提取未展平的 params 和 statistics
            param_idx = data.get('param_index')
            original_record = next((item for item in cleaned_results if item.get('param_index') == param_idx), None)

            if original_record:
                return {
                    'param_index': param_idx,
                    'params': original_record.get('params', {}),
                    'statistics': original_record.get('statistics', {}),
                    'result_dir': original_record.get('result_dir'),
                    'single_report_rel_path': original_record.get('single_report_rel_path'),
                    'execution_time': original_record.get('execution_time')
                }
            else:
                # Fallback if original record not found (should not happen normally)
                return {
                    'param_index': param_idx,
                    'params': {k.replace('params_', ''): v for k, v in data.items() if k.startswith('params_')},
                    'statistics': {k.replace('statistics_', ''): v for k, v in data.items() if k.startswith('statistics_')},
                    'result_dir': data.get('result_dir'),
                    'single_report_rel_path': data.get('single_report_rel_path'),
                    'execution_time': data.get('execution_time')
                }

        # 使用带 'statistics_' 前缀的列名查找最佳
        best_by_return_row = df.nlargest(1, 'statistics_return_rate') if 'statistics_return_rate' in df.columns else None
        best_by_win_rate_row = df.nlargest(1, 'statistics_win_rate') if 'statistics_win_rate' in df.columns else None
        
        best_by_return_data = extract_best_data(best_by_return_row)
        best_by_win_rate_data = extract_best_data(best_by_win_rate_row)

        # --- 渲染 HTML 模板 ---
        template_dir = os.path.dirname(__file__) # 模板文件和脚本在同一个目录
        env = Environment(loader=FileSystemLoader(template_dir))
        try:
            template = env.get_template('report_template.html')
        except exceptions.TemplateNotFound:
             logger.error(f"错误：未找到模板文件 'report_template.html' 在目录 {template_dir}")
             return

        html_content = template.render(
            generation_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            result_dir=os.path.dirname(json_file_path), # 主结果目录
            total_params=len(df),
            # 时间统计信息
            total_execution_time=time_stats.get('total_execution_time_formatted', 'N/A'),
            average_execution_time=time_stats.get('average_execution_time_formatted', 'N/A'),
            # 最佳结果数据
            best_by_return=best_by_return_data,
            best_by_win_rate=best_by_win_rate_data,
            param_descriptions=PARAM_DESCRIPTIONS, # 传递参数描述
            # 表格和图表数据
            results_table=results_table_html,
            scatter_data_json=scatter_data_json,
            bar_return_data_json=bar_return_data_json,
            bar_winrate_data_json=bar_winrate_data_json,
            top_n=top_n_results,
            # 修改: 移除重复的 param_descriptions
            metrics_info=metrics_for_bars, # 传递指标信息给模板 (可能不需要了)
        )

        # 保存 HTML 文件
        with open(output_html_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        logger.info(f"HTML 报告已成功保存到: {output_html_path}")

    except FileNotFoundError:
        logger.error(f"错误：未找到结果 JSON 文件 {json_file_path}")
    except json.JSONDecodeError:
         logger.error(f"错误：解析结果 JSON 文件 {json_file_path} 失败")
    except KeyError as e:
         logger.error(f"错误：结果 JSON 文件中缺少键: {e}", exc_info=True)
    except Exception as e:
        logger.error(f"生成 HTML 报告时发生意外错误: {e}", exc_info=True)

# --- Function to generate single run report ---
def generate_single_run_report(result_json_path: str, output_html_path: str) -> bool:
    """
    为单次回测运行从 JSON 文件生成 HTML 报告。

    Args:
        result_json_path (str): 包含单次回测结果的 JSON 文件的路径。
        output_html_path (str): 要保存 HTML 报告的路径。

    Returns:
        bool: 如果报告生成成功则为 True，否则为 False。
    """
    logger.info(f"开始为 {result_json_path} 生成单次运行报告...")
    try:
        with open(result_json_path, 'r') as f:
            data = json.load(f)
    except FileNotFoundError:
        logger.error(f"错误: JSON 文件未找到: {result_json_path}")
        return False
    except json.JSONDecodeError:
        logger.error(f"错误: 无法解码 JSON 文件: {result_json_path}")
        return False

    # 提取所需数据
    param_index = data.get("param_index", "N/A")
    config_params = data.get("config", data.get("params", {})) # 优先用 config，然后 params
    statistics = data.get("statistics", {})
    
    # 从统计中移除夏普比率
    if 'sharpe_ratio' in statistics:
        del statistics['sharpe_ratio']
    
    # 提取交易数据并移除token_info字段 (避免显示过长的token_info)
    trades_data = data.get("trades", [])
    for trade in trades_data:
        if "token_info" in trade:
            del trade["token_info"]

    # 定义格式化时间戳的函数
    def format_time(ts):
        """格式化时间戳为可读字符串"""
        if isinstance(ts, (int, float)):
            try:
                return datetime.fromtimestamp(ts).strftime("%Y-%m-%d %H:%M:%S")
            except (ValueError, OverflowError, OSError):
                return str(ts)
        return str(ts)

    # 格式化后的参数和统计信息，用于展示
    config_params_display = {}
    for k, v in config_params.items():
        if isinstance(v, (int, float)) and v > 1000000000 and v < 10000000000:
            # 可能是时间戳，尝试格式化
            config_params_display[k] = format_time(v)
        else:
            config_params_display[k] = v

    statistics_display = {}
    for k, v in statistics.items():
        # 跳过原始的execution_time字段，只保留格式化后的
        if k == 'execution_time':
            continue
        if isinstance(v, (int, float)) and v > 1000000000 and v < 10000000000:
            # 可能是时间戳，尝试格式化
            statistics_display[k] = format_time(v)
        else:
            statistics_display[k] = v

    # 准备交易数据的 DataFrame
    if trades_data:
        try:
            trades_df = pd.DataFrame(trades_data)
            
            # 尝试格式化时间戳字段
            if 'timestamp' in trades_df.columns:
                trades_df['timestamp'] = trades_df['timestamp'].apply(format_time)
                
            # 添加代码：将列名替换为中文
            column_translations = {
                # 基础字段
                "timestamp": "时间戳",
                "token_address": "代币地址",
                "type": "类型",
                "amount": "数量",
                "price": "价格",
                "cost": "成本",
                "commission": "手续费",
                "total_invested": "总投资",
                "total_value": "总价值",
                "proceeds": "收益",
                "realized_pnl": "已实现盈亏",
                "unrealized_pnl": "未实现盈亏",
                "quantity": "买入金额(USD)",
                "cost_usd": "成本($)",
                "commission_usd": "手续费($)",
                "cost_basis": "持有成本($)",
                "event_type": "事件来源",
                
                # 回测V2相关字段
                "buy_timestamp": "买入时间",
                "sell_timestamp": "卖出时间", 
                "buy_price": "买入价格",
                "sell_price": "卖出价格",
                "buy_reason": "买入原因",
                "sell_reason": "卖出原因",
                "kol_count": "KOL数量",
                "holding_hours": "持仓时间(分钟)",
                "return_rate": "收益率",
                "profit_usd": "盈亏(USD)"
            }
            
            # 隐藏买入原因列（因为都是kol_signal）
            if 'buy_reason' in trades_df.columns:
                trades_df = trades_df.drop('buy_reason', axis=1)
            
            # 卖出原因翻译字典
            sell_reason_translations = {
                'kol_ratio': 'KOL比例卖出',
                'timeout': '超时卖出',
                'stop_loss': '止损卖出',
                'take_profit': '止盈卖出'
            }
            
            # 翻译卖出原因为中文
            if 'sell_reason' in trades_df.columns:
                trades_df['sell_reason'] = trades_df['sell_reason'].apply(
                    lambda x: sell_reason_translations.get(x, x) if pd.notna(x) else x
                )
            
            # 只替换存在的列
            new_columns = {}
            for col in trades_df.columns:
                if col in column_translations:
                    new_columns[col] = column_translations[col]
            
            # 重命名列
            if new_columns:
                trades_df.rename(columns=new_columns, inplace=True)
            
            # 格式化特殊列
            def format_return_rate(rate):
                """格式化收益率为带颜色的百分比"""
                if pd.isna(rate):
                    return ''
                try:
                    rate_float = float(rate)
                    percentage = rate_float * 100
                    color = 'green' if rate_float >= 0 else 'red'
                    return f'<span style="color: {color}; font-weight: bold;">{percentage:.2f}%</span>'
                except (ValueError, TypeError):
                    return str(rate)
            
            # 格式化持仓时间为2位小数
            def format_holding_time(time_val):
                """格式化持仓时间为2位小数"""
                if pd.isna(time_val):
                    return ''
                try:
                    time_float = float(time_val)
                    return f"{time_float:.2f}"
                except (ValueError, TypeError):
                    return str(time_val)
            
            # 格式化盈亏为2位小数
            def format_profit_usd(profit_val):
                """格式化盈亏为2位小数"""
                if pd.isna(profit_val):
                    return ''
                try:
                    profit_float = float(profit_val)
                    color = 'green' if profit_float >= 0 else 'red'
                    return f'<span style="color: {color}; font-weight: bold;">{profit_float:.2f}</span>'
                except (ValueError, TypeError):
                    return str(profit_val)
            
            # 应用格式化
            if '收益率' in trades_df.columns:
                trades_df['收益率'] = trades_df['收益率'].apply(format_return_rate)
            
            if '持仓时间(分钟)' in trades_df.columns:
                trades_df['持仓时间(分钟)'] = trades_df['持仓时间(分钟)'].apply(format_holding_time)
                
            if '盈亏(USD)' in trades_df.columns:
                trades_df['盈亏(USD)'] = trades_df['盈亏(USD)'].apply(format_profit_usd)
            
            # 调整列顺序：把收益率和买入金额位置互换
            if '收益率' in trades_df.columns and '买入金额(USD)' in trades_df.columns:
                cols = list(trades_df.columns)
                
                # 找到两列的索引
                return_rate_idx = cols.index('收益率')
                quantity_idx = cols.index('买入金额(USD)')
                
                # 交换位置
                cols[return_rate_idx], cols[quantity_idx] = cols[quantity_idx], cols[return_rate_idx]
                
                # 重新排列DataFrame列
                trades_df = trades_df[cols]
                
            # 生成 HTML 表格
            trades_html = trades_df.to_html(index=False, classes=["dataframe", "results-table"], 
                                        table_id="trades-table", na_rep='', escape=False, border=0)
        except Exception as e:
            logger.error(f"生成交易表格时出错: {e}")
            trades_html = "<p>生成交易表格时出错。</p>"
    else:
        trades_html = "<p>没有交易记录。</p>"

    # 准备模板渲染上下文
    context = {
        "param_index": param_index,
        "config": config_params_display,
        "statistics": statistics_display,
        "trades_html": trades_html,
        "generation_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "source_file": os.path.basename(result_json_path),
        "execution_time": statistics.get("execution_time_formatted", statistics_display.get("execution_time_formatted", "N/A")),
        "param_descriptions": PARAM_DESCRIPTIONS,
        "hidden_params": HIDDEN_PARAMS
    }

    try:
        # 使用外部模板文件
        template_dir = os.path.dirname(__file__)
        env = Environment(
            loader=FileSystemLoader(template_dir),
            autoescape=select_autoescape(['html', 'xml'])
        )
        
        template = env.get_template('single_run_report_template.html')
        html_content = template.render(**context)

        with open(output_html_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        logger.info(f"单次运行报告已成功生成到: {output_html_path}")
        return True

    except Exception as e:
        logger.error(f"生成单次运行报告时出错: {e}", exc_info=True)
        return False
