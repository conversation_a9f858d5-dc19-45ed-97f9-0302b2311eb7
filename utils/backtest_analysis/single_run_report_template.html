<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单次回测运行详情 - 参数组合 {{ param_index }}</title>
    <style>
        /* --- Material Design Inspired Styles (Subset) --- */
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
            margin: 0;
            padding: 24px;
            line-height: 1.6;
            color: #212121;
            background-color: #f5f5f5;
        }
        .container {
             background-color: #fff;
             padding: 24px;
             margin: 24px auto;
             max-width: 1200px; /* Adjusted max width */
             border-radius: 8px;
             box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1, h2, h3 {
            color: #333;
            font-weight: 500;
        }
        h1 {
            font-size: 1.8em; /* Slightly smaller */
            border-bottom: 1px solid #e0e0e0;
            padding-bottom: 16px;
            margin-bottom: 8px;
        }
        h1 + p {
            font-size: 0.8em;
            color: #757575;
            margin-bottom: 24px;
        }
        h2 {
            font-size: 1.4em; /* Smaller */
            margin-top: 24px;
            border-bottom: 1px solid #eee;
            padding-bottom: 8px;
        }
        .section {
            margin-bottom: 32px;
        }
        .code {
            background-color: #f0f0f0;
            padding: 3px 6px;
            border-radius: 4px;
            font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;
            font-size: 0.9em;
            border: 1px solid #e0e0e0;
            word-break: break-all; /* Allow long paths to break */
        }
        /* Parameter Table Style (Copied from main template) */
        .param-table {
            width: 100%;
            margin-top: 10px;
            border-collapse: collapse;
            font-size: 0.9em;
            table-layout: fixed; /* 确保列宽固定 */
        }
        .param-table td {
            padding: 8px 10px; /* Increased padding */
            border: 1px solid #eee; /* Add subtle borders */
            word-wrap: break-word; /* Allow wrapping */
            vertical-align: top;
        }
        /* 修改：为4列表格设置固定宽度 */
        .param-table td:nth-child(1),
        .param-table td:nth-child(3) {
            width: 40%; /* 第一列和第三列（参数名）相同宽度 */
            color: #555;
            font-weight: 500;
            background-color: #f9f9f9; /* Light background for key column */
        }
        .param-table td:nth-child(2),
        .param-table td:nth-child(4) {
            width: 10%; /* 第二列和第四列（参数值）相同宽度 */
        }
        /* Statistics Table Style */
        .stats-table {
            width: 100%;
            margin-top: 10px;
            border-collapse: collapse;
            font-size: 0.9em;
        }
        .stats-table td {
            padding: 8px 10px;
            border: 1px solid #eee;
        }
        .stats-table td:first-child {
            width: 40%;
            color: #555;
            font-weight: 500;
            background-color: #f9f9f9;
        }
        /* Metric value colors & styles (Copied) */
        .metric-value {
            font-weight: bold;
        }
        .metric-positive { color: green; }
        .metric-negative { color: red; }
        .metric-neutral { color: orange; }
        .metric-na { color: grey; }

        /* 关键指标突出显示 */
        .key-metric-row td {
            background-color: #f0f8ff !important; /* 浅蓝色背景 */
            font-weight: 500;
        }
        .key-metric-row td:first-child {
            background-color: #e6f2ff !important; /* 更深一点的蓝色 */
            font-weight: 600;
        }

        /* Improved Table Styles for Trades (Copied) */
        .table-container {
            overflow-x: auto;
            width: 100%;
            margin-top: 20px;
            /* border: 1px solid #e0e0e0; 移除边框 */
            border-radius: 4px;
        }
        .results-table {
            border-collapse: collapse;
            width: 100%;
            margin-bottom: 0;
            font-size: 0.875em;
        }
        .results-table th,
        .results-table td {
            border: none; /* 移除所有边框 */
            padding: 10px 14px; /* 保留内边距 */
            text-align: left;
            white-space: nowrap;
        }
        .results-table tr:last-child td {
            border-bottom: none;
        }
        .results-table th {
            background-color: #f5f5f5;
            font-weight: 500;
            color: #757575;
        }
        
        /* 买入和卖出行的不同背景色 */
        .results-table tr.buy-row {
            background-color: rgba(232, 245, 233, 0.5); /* 浅绿色背景 */
        }
        .results-table tr.buy-row:hover td {
            background-color: rgba(232, 245, 233, 0.8) !important; /* 鼠标悬停加深 */
        }
        .results-table tr.sell-row {
            background-color: rgba(255, 235, 238, 0.5); /* 浅红色背景 */
        }
        .results-table tr.sell-row:hover td {
            background-color: rgba(255, 235, 238, 0.8) !important; /* 鼠标悬停加深 */
        }
        
        .results-table tbody tr:hover td {
            background-color: #f0f0f0;
        }

        /* 关键指标卡片样式 */
        .key-metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .metric-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .metric-card:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .highlight-card {
            background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
            border: 2px solid #ff9800;
            box-shadow: 0 2px 10px rgba(255, 152, 0, 0.2);
        }
        
        .metric-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
            font-weight: 500;
        }
        
        .metric-value-large {
            font-size: 24px;
            font-weight: bold;
            line-height: 1.2;
        }

        /* 指标区域样式 */
        .metrics-section {
            margin-bottom: 25px;
            background: #fafafa;
            border-radius: 8px;
            padding: 20px;
        }
        
        .metrics-section h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 16px;
            font-weight: 600;
            border-bottom: 2px solid #2196f3;
            padding-bottom: 5px;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 12px;
        }
        
        .metric-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 15px;
            background: white;
            border-radius: 6px;
            border-left: 4px solid #e0e0e0;
            transition: all 0.2s ease;
        }
        
        .metric-item:hover {
            border-left-color: #2196f3;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .metric-name {
            font-weight: 500;
            color: #555;
            flex: 1;
        }
        
        .metric-value {
            font-weight: bold;
            text-align: right;
            margin-left: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>单次回测运行详情 - 参数组合 {{ param_index }}</h1>
        <p>报告生成时间: {{ generation_time }} | 来源: <code class="code">{{ source_file }}</code></p>

        <div class="section">
            <h2>参数配置</h2>
            <!-- Use the new two-column table structure -->
            <table class="param-table">
                <tbody>
                {% set count = namespace(value=0) %} {# Jinja namespace to allow modifying variable inside loop #}
                {% for key, value in config.items() %}
                    {% if key not in hidden_params %}
                        {% if count.value % 2 == 0 %} <tr> {% endif %} {# Start a new row every 2 parameters #}
                            <td><code class="code">{{ param_descriptions.get(key, key) }}</code> ({{ key }})</td>
                            <td>{{ value | default('N/A') }}</td>
                        {% if count.value % 2 != 0 or loop.last %} </tr> {% endif %} {# End row after 2 parameters or on last item #}
                        {% set count.value = count.value + 1 %}
                    {% endif %}
                {% endfor %}
                {# Ensure the last row is closed if the total number of params is odd #}
                {% if count.value % 2 != 0 %} </tr> {% endif %}
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>统计摘要</h2>
            
            <!-- 关键指标卡片 -->
            <div class="key-metrics-grid">
                {% set key_metrics = ['total_trades', 'winning_trades', 'losing_trades', 'win_rate', 'return_rate'] %}
                {% for key in key_metrics %}
                    {% if key in statistics %}
                        {% set value = statistics[key] %}
                        {% set val_float = value | float(default=0.0) %}
                        <div class="metric-card{% if key in ['win_rate', 'return_rate'] %} highlight-card{% endif %}">
                            <div class="metric-label">{{ param_descriptions.get(key, key) }}</div>
                            <div class="metric-value-large
                                {% if key == 'win_rate' and val_float > 0.5 %}metric-positive
                                {% elif key == 'win_rate' %}metric-negative
                                {% elif key == 'return_rate' and val_float > 0 %}metric-positive
                                {% elif key == 'return_rate' %}metric-negative
                                {% else %}metric-neutral
                                {% endif %}
                            ">
                                {% if key in ['win_rate', 'return_rate'] %}
                                    {{ "%.2f%%" | format(val_float * 100) }}
                                {% else %}
                                    {{ value }}
                                {% endif %}
                            </div>
                        </div>
                    {% endif %}
                {% endfor %}
            </div>

            <!-- 收益相关指标 -->
            <div class="metrics-section">
                <h3>收益分析</h3>
                <div class="metrics-grid">
                    {% set profit_metrics = ['avg_winning_return', 'avg_losing_return', 'max_drawdown', 'max_profit', 'profit_loss_ratio'] %}
                    {% for key in profit_metrics %}
                        {% if key in statistics %}
                            {% set value = statistics[key] %}
                            {% set val_float = value | float(default=0.0) %}
                            <div class="metric-item">
                                <span class="metric-name">{{ param_descriptions.get(key, key) }}:</span>
                                <span class="metric-value
                                    {% if key == 'max_drawdown' %}metric-negative
                                    {% elif key == 'max_profit' and val_float > 0 %}metric-positive
                                    {% elif key in ['avg_winning_return'] and val_float > 0 %}metric-positive
                                    {% elif key in ['avg_losing_return'] %}metric-negative
                                    {% elif key == 'profit_loss_ratio' and val_float > 1 %}metric-positive
                                    {% else %}metric-neutral
                                    {% endif %}
                                ">
                                    {% if key in ['avg_winning_return', 'avg_losing_return'] %}
                                        {{ "%.2f%%" | format(val_float * 100) }}
                                    {% elif key == 'max_drawdown' %}
                                        {{ "%.2f%%" | format(val_float | abs * 100) }}
                                    {% elif key == 'max_profit' %}
                                        {{ "%.2f%%" | format(val_float * 100) }}
                                    {% else %}
                                        {{ "%.2f" | format(val_float) }}
                                    {% endif %}
                                </span>
                            </div>
                        {% endif %}
                    {% endfor %}
                </div>
            </div>

            <!-- 时间相关指标 -->
            <div class="metrics-section">
                <h3>时间分析</h3>
                <div class="metrics-grid">
                    {% set time_metrics = ['avg_holding_minutes', 'max_holding_minutes', 'min_holding_minutes', 'backtest_start_time', 'backtest_end_time', 'backtest_days'] %}
                    {% for key in time_metrics %}
                        {% if key in statistics %}
                            {% set value = statistics[key] %}
                            <div class="metric-item">
                                <span class="metric-name">{{ param_descriptions.get(key, key) }}:</span>
                                <span class="metric-value metric-neutral">
                                    {% if key in ['backtest_start_time', 'backtest_end_time'] %}
                                        {{ value }}
                                    {% else %}
                                        {{ "%.2f" | format(value | float(default=0.0)) }}
                                    {% endif %}
                                </span>
                            </div>
                        {% endif %}
                    {% endfor %}
                </div>
            </div>

            <!-- 其他指标 -->
            <div class="metrics-section">
                <h3>其他指标</h3>
                <div class="metrics-grid">
                    {% for key, value in statistics.items() %}
                        {% if key not in ['sell_reason_stats', 'kol_count_stats', 'total_trades', 'winning_trades', 'losing_trades', 'win_rate', 'return_rate', 'avg_winning_return', 'avg_losing_return', 'max_drawdown', 'max_profit', 'profit_loss_ratio', 'avg_holding_minutes', 'max_holding_minutes', 'min_holding_minutes', 'backtest_start_time', 'backtest_end_time', 'backtest_days'] %}
                            {% set val_float = value | float(default=0.0) %}
                            <div class="metric-item">
                                <span class="metric-name">{{ param_descriptions.get(key, key) }}:</span>
                                <span class="metric-value
                                    {% if key == 'kelly_fraction_calculated' and val_float > 0 %}metric-positive
                                    {% elif key == 'kelly_fraction_calculated' %}metric-negative
                                    {% else %}metric-neutral
                                    {% endif %}
                                ">
                                    {% if value is none or value == 'N/A' %}
                                        N/A
                                    {% elif key == 'kelly_fraction_calculated' %}
                                        {{ "%.4f" | format(val_float) }}
                                    {% elif key == 'max_profit_per_trade' %}
                                        {{ "%.2f" | format(val_float) }}
                                    {% elif key in ['total_pnl', 'total_invested', 'final_value', 'total_profit_usd', 'total_loss_usd', 'net_profit_usd', 'fixed_trade_amount', 'total_commission_usd'] %}
                                        {{ "%.2f" | format(val_float) }}
                                    {% else %}
                                        {{ value }}
                                    {% endif %}
                                </span>
                            </div>
                        {% endif %}
                    {% endfor %}
                    
                    <!-- 执行时间 -->
                    <div class="metric-item">
                        <span class="metric-name">执行时间:</span>
                        <span class="metric-value metric-neutral">{{ execution_time | default('N/A') }}</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>交易记录</h2>
            <div class="table-container">
                {{ trades_html | safe }}
            </div>
            
            <script>
                // 为买入和卖出行添加CSS类
                document.addEventListener('DOMContentLoaded', function() {
                    const tradeRows = document.querySelectorAll('.results-table tbody tr');
                    tradeRows.forEach(row => {
                        const typeCells = row.querySelectorAll('td');
                        if (typeCells.length > 2) {
                            const typeCell = typeCells[2]; // 假设type列是第3列
                            if (typeCell.textContent.trim().toUpperCase() === 'BUY') {
                                row.classList.add('buy-row');
                            } else if (typeCell.textContent.trim().toUpperCase() === 'SELL') {
                                row.classList.add('sell-row');
                            }
                        }
                    });
                });
            </script>
        </div>

    </div>
</body>
</html> 