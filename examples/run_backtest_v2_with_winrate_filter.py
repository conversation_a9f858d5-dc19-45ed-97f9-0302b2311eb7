#!/usr/bin/env python3
"""
回测模块V2 KOL总胜率过滤功能使用示例

此脚本演示如何使用新增的KOL总胜率过滤功能进行回测。
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.backtest_v2.config_manager import BacktestConfigV2, ConfigManagerV2
from utils.backtest_v2.backtest_engine import BacktestEngineV2
from utils.database import init_db


async def run_backtest_with_winrate_filter():
    """运行带有KOL胜率过滤的回测示例"""
    
    # 初始化数据库连接
    await init_db()
    
    print("🚀 回测模块V2 - KOL总胜率过滤功能演示")
    print("=" * 50)
    
    # 示例1: 不过滤胜率（默认行为）
    print("\n📊 示例1: 不过滤KOL胜率 (kol_min_winrate=0.0)")
    config1 = BacktestConfigV2(
        backtest_start_time=**********,  # 2023-01-01
        backtest_end_time=**********,    # 2023-01-02 (1天测试)
        kol_min_winrate=0.0,             # 不过滤
        kol_account_min_count=3,         # 降低要求便于测试
        transaction_min_amount=300.0     # 降低要求便于测试
    )
    
    engine1 = BacktestEngineV2(config1)
    try:
        result1 = await engine1.run_backtest()
        print(f"✅ 无胜率过滤回测完成，找到 {len(result1.get('trades', []))} 个交易")
    except Exception as e:
        print(f"❌ 无胜率过滤回测失败: {e}")
    
    # 示例2: 过滤胜率 > 50%
    print("\n📊 示例2: 过滤KOL胜率 > 50% (kol_min_winrate=0.5)")
    config2 = BacktestConfigV2(
        backtest_start_time=**********,  # 2023-01-01
        backtest_end_time=**********,    # 2023-01-02 (1天测试)
        kol_min_winrate=0.5,             # 过滤胜率 > 50%
        kol_account_min_count=3,         # 降低要求便于测试
        transaction_min_amount=300.0     # 降低要求便于测试
    )
    
    engine2 = BacktestEngineV2(config2)
    try:
        result2 = await engine2.run_backtest()
        print(f"✅ 胜率过滤回测完成，找到 {len(result2.get('trades', []))} 个交易")
    except Exception as e:
        print(f"❌ 胜率过滤回测失败: {e}")
    
    # 示例3: 过滤胜率 > 70% (更严格)
    print("\n📊 示例3: 过滤KOL胜率 > 70% (kol_min_winrate=0.7)")
    config3 = BacktestConfigV2(
        backtest_start_time=**********,  # 2023-01-01
        backtest_end_time=**********,    # 2023-01-02 (1天测试)
        kol_min_winrate=0.7,             # 过滤胜率 > 70%
        kol_account_min_count=2,         # 进一步降低要求
        transaction_min_amount=300.0     # 降低要求便于测试
    )
    
    engine3 = BacktestEngineV2(config3)
    try:
        result3 = await engine3.run_backtest()
        print(f"✅ 严格胜率过滤回测完成，找到 {len(result3.get('trades', []))} 个交易")
    except Exception as e:
        print(f"❌ 严格胜率过滤回测失败: {e}")
    
    print("\n" + "=" * 50)
    print("💡 使用说明:")
    print("1. kol_min_winrate=0.0: 不过滤，包含所有KOL（默认）")
    print("2. kol_min_winrate=0.5: 只包含总胜率 > 50% 的KOL")
    print("3. kol_min_winrate=0.7: 只包含总胜率 > 70% 的KOL")
    print("4. 胜率数据来源: gmgn_wallet_stats表中period='all'的winrate字段")
    print("5. 过滤逻辑: 在MongoDB聚合查询中自动过滤低胜率KOL")
    
    print("\n🎯 预期效果:")
    print("- 胜率阈值越高，符合条件的KOL越少")
    print("- 符合条件的KOL越少，产生的交易信号越少")
    print("- 但信号质量可能更高（来自高胜率KOL）")


async def run_from_config_file():
    """从配置文件运行回测示例"""
    
    print("\n🔧 从配置文件运行回测示例")
    print("=" * 50)
    
    # 加载配置文件
    config_path = "examples/backtest_v2_kol_winrate_config.yaml"
    if os.path.exists(config_path):
        try:
            config = ConfigManagerV2.load_from_file(config_path)
            print(f"✅ 成功加载配置文件: {config_path}")
            print(f"📊 KOL胜率阈值: {config.kol_min_winrate}")
            
            # 运行回测
            engine = BacktestEngineV2(config)
            result = await engine.run_backtest()
            print(f"✅ 配置文件回测完成，找到 {len(result.get('trades', []))} 个交易")
            
        except Exception as e:
            print(f"❌ 配置文件回测失败: {e}")
    else:
        print(f"⚠️  配置文件不存在: {config_path}")


if __name__ == "__main__":
    print("🎯 回测模块V2 - KOL总胜率过滤功能演示")
    print("此功能允许根据KOL的历史总胜率过滤交易信号")
    print("胜率数据来自gmgn_wallet_stats表，提高信号质量")
    
    # 运行示例
    asyncio.run(run_backtest_with_winrate_filter())
    asyncio.run(run_from_config_file())
